import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.io.UnsupportedEncodingException;
import java.util.Scanner;

public class TestAPI {
    private static final String API_URL = "http://api-cf.zjdanli.com/cloudflare/getCookie";
    private static final String APP_ID = "71gcowhtptnexn8c3dqx0kphsnt7snpv";
    
    public static void main(String[] args) {
        System.out.println("🔍 测试Cloudflare绕过API");
        System.out.println("API地址: " + API_URL);
        System.out.println("APP ID: " + APP_ID);
        System.out.println("==================================================");
        
        // 测试不同的域名
        String[] testHosts = {
            "baidu.com",
            "taobao.com",
            "qq.com",
            "jd.com",
            "tmall.com",
            "weibo.com"
        };
        
        for (String host : testHosts) {
            System.out.println("\n📋 测试域名: " + host);
            testGetCookie(host);
        }
    }
    
    private static void testGetCookie(String host) {
        try {
            // 构建JSON请求体
            String jsonBody = String.format("{\"appid\":\"%s\",\"host\":\"%s\"}", APP_ID, host);
            System.out.println("请求体: " + jsonBody);
            
            // 创建连接
            URL url = new URL(API_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法和头部
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setDoOutput(true);
            
            // 发送请求体
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonBody.getBytes("UTF-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            Scanner scanner;
            if (responseCode >= 200 && responseCode < 300) {
                scanner = new Scanner(conn.getInputStream(), "UTF-8");
            } else {
                scanner = new Scanner(conn.getErrorStream(), "UTF-8");
            }
            
            StringBuilder response = new StringBuilder();
            while (scanner.hasNextLine()) {
                response.append(scanner.nextLine());
            }
            scanner.close();
            
            System.out.println("响应内容: " + response.toString());
            
            // 简单解析响应
            String responseStr = response.toString();
            if (responseStr.contains("\"code\":200") && responseStr.contains("\"cookie\"")) {
                System.out.println("✅ 成功获取Cookie");
                
                // 提取cookie值
                int cookieStart = responseStr.indexOf("\"cookie\":\"") + 10;
                int cookieEnd = responseStr.indexOf("\"", cookieStart);
                if (cookieStart > 9 && cookieEnd > cookieStart) {
                    String cookie = responseStr.substring(cookieStart, cookieEnd);
                    System.out.println("Cookie预览: " + cookie.substring(0, Math.min(50, cookie.length())) + "...");
                }
            } else {
                System.out.println("❌ 获取Cookie失败");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 请求异常: " + e.getMessage());
        }
    }
}
