import javax.swing.*;
import java.awt.*;

public class SimpleGUITest {
    public static void main(String[] args) {
        System.out.println("开始创建简单GUI测试...");
        
        SwingUtilities.invokeLater(() -> {
            System.out.println("在EDT中创建GUI...");
            
            JFrame frame = new JFrame("简单GUI测试");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(600, 400);
            frame.setLocationRelativeTo(null);
            
            JPanel panel = new JPanel(new BorderLayout());
            
            JLabel titleLabel = new JLabel("这是一个简单的GUI测试", JLabel.CENTER);
            titleLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 20));
            panel.add(titleLabel, BorderLayout.NORTH);
            
            JTextArea textArea = new JTextArea("如果你能看到这个窗口，说明Java GUI工作正常。\n\n" +
                "这个测试用来验证GUI环境是否正确配置。\n" +
                "如果这个窗口显示正常，那么问题可能在MainFrame类中。");
            textArea.setEditable(false);
            textArea.setMargin(new Insets(20, 20, 20, 20));
            panel.add(new JScrollPane(textArea), BorderLayout.CENTER);
            
            JButton button = new JButton("测试按钮");
            button.addActionListener(e -> {
                JOptionPane.showMessageDialog(frame, "按钮点击测试成功！", "测试", JOptionPane.INFORMATION_MESSAGE);
            });
            panel.add(button, BorderLayout.SOUTH);
            
            frame.add(panel);
            frame.setVisible(true);
            
            System.out.println("GUI创建完成并显示");
        });
    }
}
