# Censys SHA256 证书扫描工具

这个工具允许你使用证书的SHA256指纹在Censys平台上搜索使用相同证书的所有域名和IP地址。

## 功能特点

- 根据用户提供的SHA256指纹查询Censys平台
- 自动提取搜索结果中的"All Names"内容
- 支持多种提取策略，确保获取完整的域名/IP列表
- 自动过滤无关的域名（如censys.io、google.com等）
- 支持输出到文件
- 详细的日志记录和错误处理

## 安装依赖

确保已安装所需的Python包：

```bash
pip install curl-cffi beautifulsoup4 lxml
```

## 使用方法

### 基本用法

```bash
python censys_sha256_scanner.py "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469"
```

### 使用Cookie（推荐）

为了获得更好的搜索结果，建议提供Censys平台的cookie：

```bash
python censys_sha256_scanner.py "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469" --cookie "your_censys_cookie_here"
```

### 保存结果到文件

```bash
python censys_sha256_scanner.py "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469" --output results.txt
```

### 静默模式（只输出结果）

```bash
python censys_sha256_scanner.py "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469" --quiet
```

## 参数说明

- `sha256`: 必需参数，证书的SHA256指纹（64位十六进制字符串）
- `--cookie`: 可选，Censys平台的cookie字符串
- `--output, -o`: 可选，将结果保存到指定文件
- `--verbose, -v`: 可选，显示详细信息（默认开启）
- `--quiet, -q`: 可选，静默模式，只输出域名/IP列表

## 配置文件

你可以创建一个`config.py`文件来设置默认配置：

```python
# config.py
CENSYS_COOKIE = "your_censys_cookie_here"
REQUEST_TIMEOUT = 30
VERBOSE = True
```

## 获取Censys Cookie

1. 在浏览器中登录Censys平台 (https://platform.censys.io/)
2. 打开开发者工具（F12）
3. 进入Network标签页
4. 在Censys平台上进行一次搜索
5. 在Network标签页中找到搜索请求
6. 复制请求头中的Cookie值

## 输出示例

```
============================================================
Censys SHA256 证书扫描工具
============================================================
目标SHA256: e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469
============================================================

找到 15 个域名/IP:
============================================================
  1. example.com
  2. www.example.com
  3. api.example.com
  4. *************
  5. *********
  ...

搜索完成!
```

## 错误处理

工具会处理以下常见错误：

- **403 Forbidden**: 访问被拒绝，可能需要登录或遇到Cloudflare保护
- **401 Unauthorized**: 未授权访问，请检查cookie是否有效
- **429 Rate Limited**: 请求过于频繁，请稍后再试
- **Invalid SHA256**: SHA256格式不正确（必须是64位十六进制字符串）

## 注意事项

1. 建议使用有效的Censys账户cookie以获得最佳搜索结果
2. 请遵守Censys平台的使用条款和频率限制
3. 工具会自动保存响应内容到HTML文件用于调试
4. 某些搜索结果可能需要登录才能查看完整内容

## 技术实现

- 使用`curl_cffi`库模拟Chrome浏览器请求，绕过基本的反爬虫检测
- 使用`BeautifulSoup`解析HTML内容
- 多种策略提取域名/IP信息：
  - 查找"All Names"相关区域
  - 解析表格中的主机信息
  - 提取链接中的主机数据
  - 正则表达式匹配域名和IP模式
- 自动验证和过滤提取的域名/IP地址
