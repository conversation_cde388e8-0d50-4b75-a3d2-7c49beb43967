#!/usr/bin/env python3
"""
Censys Cookie助手工具
帮助获取和测试Censys平台的cookie
"""

import time
import argparse
from urllib.parse import quote
from curl_cffi import requests
from bs4 import BeautifulSoup

class CensysCookieHelper:
    def __init__(self, verbose=True):
        self.session = requests.Session(impersonate="chrome120")
        self.verbose = verbose
        
        # 设置更完整的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
        })

    def log(self, message, level="INFO"):
        """输出日志信息"""
        if self.verbose:
            timestamp = time.strftime('%H:%M:%S')
            print(f"[{timestamp}] {level}: {message}")

    def test_cookie_access(self, cookie_string, test_sha256=None):
        """
        测试cookie是否能够访问Censys搜索功能
        """
        if not test_sha256:
            test_sha256 = "3d4949784246fff7529b6b82df7e544bf9bad834141d2167634e5b62a1d885b5"
        
        self.log("测试cookie访问权限...")
        
        # 设置cookie
        if cookie_string:
            self.set_cookies_from_string(cookie_string)
        
        # 测试不同的URL
        test_urls = [
            f"https://platform.censys.io/search?q=cert.fingerprint_sha256%20%3D%20%22{test_sha256}%22",
            f"https://search.censys.io/certificates?q=cert.fingerprint_sha256%3D{test_sha256}",
            "https://platform.censys.io/",
            "https://search.censys.io/",
        ]
        
        results = {}
        
        for url in test_urls:
            self.log(f"测试URL: {url}")
            try:
                response = self.session.get(url, timeout=30)
                results[url] = {
                    'status_code': response.status_code,
                    'title': self.extract_title(response.text),
                    'is_login_page': 'login' in response.text.lower() or 'sign in' in response.text.lower(),
                    'has_search_results': 'search' in response.text.lower() and 'results' in response.text.lower(),
                    'content_length': len(response.text)
                }
                self.log(f"状态码: {response.status_code}, 标题: {results[url]['title']}")
                
                # 保存响应用于调试
                filename = f"test_response_{url.replace('https://', '').replace('/', '_')}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.log(f"响应已保存到: {filename}")
                
            except Exception as e:
                results[url] = {'error': str(e)}
                self.log(f"请求失败: {e}", "ERROR")
        
        return results

    def set_cookies_from_string(self, cookie_string):
        """从cookie字符串设置cookies"""
        if not cookie_string:
            return
        
        self.log("设置cookies...")
        
        # 解析cookie字符串
        cookies = {}
        for cookie_pair in cookie_string.split(';'):
            if '=' in cookie_pair:
                key, value = cookie_pair.strip().split('=', 1)
                cookies[key] = value
        
        # 设置cookies到不同域名
        domains = ['.censys.io', 'platform.censys.io', 'search.censys.io', 'accounts.censys.io']
        for domain in domains:
            for key, value in cookies.items():
                self.session.cookies.set(key, value, domain=domain)
        
        # 同时设置到headers
        self.session.headers['Cookie'] = cookie_string
        
        self.log(f"设置了 {len(cookies)} 个cookies到 {len(domains)} 个域名")

    def extract_title(self, html_content):
        """提取HTML页面标题"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            title_tag = soup.find('title')
            return title_tag.get_text().strip() if title_tag else "No title"
        except:
            return "Parse error"

    def get_session_cookies(self):
        """获取当前session的所有cookies"""
        cookies = {}
        for cookie in self.session.cookies:
            cookies[cookie.name] = cookie.value
        return cookies

    def try_alternative_methods(self, sha256):
        """尝试其他方法获取证书信息"""
        self.log("尝试其他方法获取证书信息...")
        
        # 方法1: 尝试crt.sh
        self.log("方法1: 尝试crt.sh...")
        try:
            crt_url = f"https://crt.sh/?q={sha256}"
            response = self.session.get(crt_url, timeout=30)
            if response.status_code == 200:
                self.log(f"crt.sh响应成功，内容长度: {len(response.text)}")
                with open(f"crt_sh_response_{sha256[:16]}.html", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                return self.parse_crt_sh_response(response.text)
        except Exception as e:
            self.log(f"crt.sh请求失败: {e}", "ERROR")
        
        # 方法2: 尝试certificate.transparency.dev
        self.log("方法2: 尝试certificate.transparency.dev...")
        try:
            ct_url = f"https://certificate.transparency.dev/search?q={sha256}"
            response = self.session.get(ct_url, timeout=30)
            if response.status_code == 200:
                self.log(f"CT响应成功，内容长度: {len(response.text)}")
                with open(f"ct_response_{sha256[:16]}.html", 'w', encoding='utf-8') as f:
                    f.write(response.text)
        except Exception as e:
            self.log(f"CT请求失败: {e}", "ERROR")
        
        return []

    def parse_crt_sh_response(self, html_content):
        """解析crt.sh响应获取域名信息"""
        domains = []
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找表格中的域名信息
            for td in soup.find_all('td'):
                text = td.get_text().strip()
                if '.' in text and len(text) < 100:
                    # 简单的域名验证
                    if self.is_valid_domain(text):
                        domains.append(text)
            
            # 去重
            domains = list(set(domains))
            self.log(f"从crt.sh提取到 {len(domains)} 个域名")
            
        except Exception as e:
            self.log(f"解析crt.sh响应失败: {e}", "ERROR")
        
        return domains

    def is_valid_domain(self, text):
        """简单的域名验证"""
        if not text or len(text) < 3 or len(text) > 100:
            return False
        
        # 检查是否包含无效字符
        invalid_chars = ['<', '>', '"', "'", '(', ')', '[', ']', '{', '}', ' ', '\n', '\t']
        if any(char in text for char in invalid_chars):
            return False
        
        # 检查是否有点号
        if '.' not in text:
            return False
        
        # 检查是否看起来像域名
        parts = text.split('.')
        if len(parts) < 2:
            return False
        
        # 检查每个部分
        for part in parts:
            if not part or not all(c.isalnum() or c == '-' for c in part):
                return False
            if part.startswith('-') or part.endswith('-'):
                return False
        
        return True


def main():
    parser = argparse.ArgumentParser(description='Censys Cookie助手工具')
    parser.add_argument('--cookie', help='要测试的cookie字符串')
    parser.add_argument('--sha256', help='要搜索的SHA256值', 
                       default='3d4949784246fff7529b6b82df7e544bf9bad834141d2167634e5b62a1d885b5')
    parser.add_argument('--test-only', action='store_true', help='只测试cookie，不尝试其他方法')
    parser.add_argument('--verbose', '-v', action='store_true', default=True, help='显示详细信息')
    
    args = parser.parse_args()
    
    helper = CensysCookieHelper(verbose=args.verbose)
    
    print("=" * 60)
    print("Censys Cookie助手工具")
    print("=" * 60)
    
    if args.cookie:
        print(f"测试cookie: {args.cookie[:50]}...")
        results = helper.test_cookie_access(args.cookie, args.sha256)
        
        print("\n测试结果:")
        print("-" * 40)
        for url, result in results.items():
            if 'error' in result:
                print(f"❌ {url}: {result['error']}")
            else:
                status = "✅" if result['status_code'] == 200 else "❌"
                login_status = "登录页面" if result['is_login_page'] else "非登录页面"
                print(f"{status} {url}")
                print(f"   状态码: {result['status_code']}")
                print(f"   标题: {result['title']}")
                print(f"   页面类型: {login_status}")
                print(f"   内容长度: {result['content_length']}")
                print()
    
    if not args.test_only:
        print("\n尝试其他方法获取证书信息...")
        print("-" * 40)
        domains = helper.try_alternative_methods(args.sha256)
        
        if domains:
            print(f"\n找到 {len(domains)} 个域名:")
            for i, domain in enumerate(domains, 1):
                print(f"{i:3d}. {domain}")
        else:
            print("\n未找到域名信息")
    
    print("\n完成!")


if __name__ == "__main__":
    main()
