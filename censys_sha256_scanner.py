#!/usr/bin/env python3
"""
Censys SHA256 证书扫描工具
根据用户提供的SHA256指纹查询Censys平台并提取All Names内容
"""

import re
import time
import argparse
import sys
from urllib.parse import quote
from curl_cffi import requests
from bs4 import BeautifulSoup

# 尝试导入配置文件
try:
    import config
    CENSYS_COOKIE = getattr(config, 'CENSYS_COOKIE', None)
    REQUEST_TIMEOUT = getattr(config, 'REQUEST_TIMEOUT', 30)
    VERBOSE = getattr(config, 'VERBOSE', True)
except ImportError:
    CENSYS_COOKIE = None
    REQUEST_TIMEOUT = 30
    VERBOSE = True


class CensysSHA256Scanner:
    def __init__(self, cookie=None, verbose=True):
        """
        初始化Censys SHA256扫描器
        
        Args:
            cookie (str): Censys平台的cookie字符串
            verbose (bool): 是否显示详细信息
        """
        self.session = requests.Session(impersonate="chrome110")
        self.cookie = cookie or CENSYS_COOKIE
        self.verbose = verbose
        
        # 设置更完整的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
        })
        
        if self.cookie:
            self.session.headers['Cookie'] = self.cookie

    def log(self, message, level="INFO"):
        """输出日志信息"""
        if self.verbose:
            timestamp = time.strftime('%H:%M:%S')
            print(f"[{timestamp}] {level}: {message}")

    def validate_sha256(self, sha256):
        """
        验证SHA256格式是否正确
        
        Args:
            sha256 (str): SHA256指纹
            
        Returns:
            bool: 是否为有效的SHA256格式
        """
        if not sha256:
            return False
        
        # 移除可能的空格和转换为小写
        sha256 = sha256.strip().lower()
        
        # 检查长度和字符
        if len(sha256) != 64:
            return False
        
        # 检查是否只包含十六进制字符
        return all(c in '0123456789abcdef' for c in sha256)

    def search_censys_by_sha256(self, sha256):
        """
        根据SHA256指纹在Censys平台搜索
        
        Args:
            sha256 (str): 证书SHA256指纹
            
        Returns:
            dict: 包含搜索结果的字典，包括all_names列表和原始HTML
        """
        # 验证SHA256格式
        if not self.validate_sha256(sha256):
            self.log(f"无效的SHA256格式: {sha256}", "ERROR")
            return {"all_names": [], "html": "", "error": "Invalid SHA256 format"}
        
        # 标准化SHA256格式（小写）
        sha256 = sha256.strip().lower()
        
        self.log(f"正在Censys平台搜索证书SHA256: {sha256}")
        
        if not self.cookie:
            self.log("警告: 未提供Censys cookie，可能无法访问搜索结果", "WARN")
        
        # 构建搜索查询
        search_query = f'cert.fingerprint_sha256 = "{sha256}"'
        url = f"https://platform.censys.io/search?q={quote(search_query)}"
        
        try:
            # 首先访问主页建立会话
            self.log("正在建立会话...")
            main_page_response = self.session.get('https://platform.censys.io/', timeout=REQUEST_TIMEOUT)
            self.log(f"主页响应状态码: {main_page_response.status_code}")

            # 等待一下模拟人类行为
            time.sleep(2)

            # 添加更完整的请求头
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'max-age=0',
                'Referer': 'https://platform.censys.io/',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
            }

            self.log(f"搜索URL: {url}")

            # 发送搜索请求
            response = self.session.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
            
            self.log(f"响应状态码: {response.status_code}")
            
            if response.status_code == 403:
                self.log("访问被拒绝，可能遇到Cloudflare保护或需要登录", "ERROR")
                return {"all_names": [], "html": response.text, "error": "Access denied (403)"}
            elif response.status_code == 401:
                self.log("未授权访问，请检查cookie是否有效", "ERROR")
                return {"all_names": [], "html": response.text, "error": "Unauthorized (401)"}
            elif response.status_code == 429:
                self.log("请求过于频繁，请稍后再试", "ERROR")
                return {"all_names": [], "html": response.text, "error": "Rate limited (429)"}
            
            response.raise_for_status()
            
            # 保存响应内容用于调试
            if self.verbose:
                debug_file = f'censys_response_{int(time.time())}.html'
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.log(f"响应内容已保存到 {debug_file}")
            
            # 解析搜索结果
            all_names = self.extract_all_names(response.text)
            
            if all_names:
                self.log(f"成功提取到 {len(all_names)} 个域名/IP")
            else:
                self.log("未找到All Names内容", "WARN")
            
            return {
                "all_names": all_names,
                "html": response.text,
                "error": None
            }
            
        except Exception as e:
            self.log(f"Censys搜索失败: {e}", "ERROR")
            return {"all_names": [], "html": "", "error": str(e)}

    def extract_all_names(self, html_content):
        """
        从Censys搜索结果页面提取All Names内容
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            list: 提取到的域名/IP列表
        """
        all_names = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 多种策略提取域名/IP信息
            
            # 策略1: 查找包含"All Names"的区域
            all_names_sections = soup.find_all(text=re.compile(r'All Names', re.IGNORECASE))
            for section in all_names_sections:
                parent = section.parent
                if parent:
                    # 在父元素及其兄弟元素中查找域名
                    for sibling in parent.find_next_siblings():
                        names = self.extract_names_from_element(sibling)
                        all_names.extend(names)
            
            # 策略2: 查找表格中的域名/IP列
            table_selectors = [
                'td[data-testid="ip-address"]',
                'td[data-testid="host-name"]',
                'td.ip-address',
                'td.host-name',
                'td:contains(".")',  # 包含点号的单元格
            ]
            
            for selector in table_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        text = element.get_text().strip()
                        if self.is_valid_domain_or_ip(text):
                            all_names.append(text)
                except Exception:
                    continue
            
            # 策略3: 查找链接中的主机信息
            host_links = soup.find_all('a', href=re.compile(r'/hosts/'))
            for link in host_links:
                href = link.get('href', '')
                # 从URL中提取主机信息
                match = re.search(r'/hosts/([^/?]+)', href)
                if match:
                    host = match.group(1)
                    if self.is_valid_domain_or_ip(host):
                        all_names.append(host)
            
            # 策略4: 使用正则表达式查找页面中的域名模式
            domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
            ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
            
            # 查找域名
            found_domains = re.findall(domain_pattern, html_content)
            for domain in found_domains:
                if self.is_valid_domain_or_ip(domain):
                    all_names.append(domain)
            
            # 查找IP地址
            found_ips = re.findall(ip_pattern, html_content)
            for ip in found_ips:
                if self.is_valid_domain_or_ip(ip):
                    all_names.append(ip)
            
            # 去重并排序
            all_names = sorted(list(set(all_names)))
            
            # 过滤掉明显不相关的域名
            filtered_names = []
            exclude_patterns = [
                'censys.io', 'google.com', 'cloudflare.com', 'amazonaws.com',
                'gstatic.com', 'googleapis.com', 'doubleclick.net', 'googlesyndication.com'
            ]
            
            for name in all_names:
                if not any(exclude in name.lower() for exclude in exclude_patterns):
                    filtered_names.append(name)
            
            return filtered_names
            
        except Exception as e:
            self.log(f"提取All Names失败: {e}", "ERROR")
            return []

    def extract_names_from_element(self, element):
        """从HTML元素中提取域名/IP"""
        names = []
        if element:
            text = element.get_text()
            # 使用正则表达式查找域名和IP
            domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
            ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
            
            found_domains = re.findall(domain_pattern, text)
            found_ips = re.findall(ip_pattern, text)
            
            for item in found_domains + found_ips:
                if self.is_valid_domain_or_ip(item):
                    names.append(item)
        
        return names

    def is_valid_domain_or_ip(self, text):
        """验证是否为有效的域名或IP地址"""
        if not text or len(text) > 100 or len(text) < 3:
            return False

        # 检查是否包含点号
        if '.' not in text:
            return False

        # 排除明显不是域名的文本
        invalid_chars = ['<', '>', '"', "'", '(', ')', '[', ']', '{', '}', ' ', '\n', '\t']
        if any(char in text for char in invalid_chars):
            return False

        # 简单的IP地址检查
        parts = text.split('.')
        if len(parts) == 4:
            try:
                # 检查是否所有部分都是数字且在有效范围内
                for part in parts:
                    if not part.isdigit():
                        break
                    num = int(part)
                    if not (0 <= num <= 255):
                        break
                else:
                    # 所有部分都是有效的IP段
                    return True
            except ValueError:
                pass

        # 简单的域名检查
        if len(parts) >= 2:
            # 检查每个部分是否只包含字母、数字和连字符
            for part in parts:
                if not part or not all(c.isalnum() or c == '-' for c in part):
                    return False
                if part.startswith('-') or part.endswith('-'):
                    return False
            return True

        return False


def main():
    parser = argparse.ArgumentParser(description='Censys SHA256 证书扫描工具')
    parser.add_argument('sha256', help='证书SHA256指纹')
    parser.add_argument('--cookie', help='Censys平台的cookie字符串')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--verbose', '-v', action='store_true', default=True, help='显示详细信息')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，只输出结果')
    
    args = parser.parse_args()
    
    # 静默模式
    if args.quiet:
        args.verbose = False
    
    # 创建扫描器实例
    scanner = CensysSHA256Scanner(
        cookie=args.cookie,
        verbose=args.verbose
    )
    
    if not args.quiet:
        print("=" * 60)
        print("Censys SHA256 证书扫描工具")
        print("=" * 60)
        print(f"目标SHA256: {args.sha256}")
        print("=" * 60)
    
    # 执行搜索
    result = scanner.search_censys_by_sha256(args.sha256)
    
    # 输出结果
    if result["error"]:
        if not args.quiet:
            print(f"\n错误: {result['error']}")
        sys.exit(1)
    
    all_names = result["all_names"]
    
    if not args.quiet:
        print(f"\n找到 {len(all_names)} 个域名/IP:")
        print("=" * 60)
    
    if all_names:
        for i, name in enumerate(all_names, 1):
            if args.quiet:
                print(name)
            else:
                print(f"{i:3d}. {name}")
        
        # 保存到文件
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"Censys SHA256 搜索结果\n")
                    f.write(f"SHA256: {args.sha256}\n")
                    f.write(f"搜索时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"找到 {len(all_names)} 个域名/IP:\n\n")
                    for name in all_names:
                        f.write(f"{name}\n")
                if not args.quiet:
                    print(f"\n[+] 结果已保存到: {args.output}")
            except Exception as e:
                if not args.quiet:
                    print(f"[-] 保存文件失败: {e}")
    else:
        if not args.quiet:
            print("未找到相关的域名/IP")
    
    if not args.quiet:
        print("\n搜索完成!")


if __name__ == "__main__":
    main()
