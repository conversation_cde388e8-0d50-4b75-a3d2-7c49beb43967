








<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  
  
    
      <title>Censys</title>
    
    <meta name="description" content="Censys helps organizations, individuals, and researchers find and monitor every server on the Internet to reduce exposure and improve security.">
  
  
    <!-- OG Card -->
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="en_US">
    
    <meta property="og:title" content="Censys" />
    
    <meta property="og:description" content="Censys helps organizations, individuals, and researchers find and monitor every server on the Internet to reduce exposure and improve security." />
    <meta property="og:url" content="https://search.censys.io/certificates" />
    <meta property="og:site_name" content="Censys" />
    <meta property="og:image" content="https://search.censys.io/static/img/og-censys-mark-2025.png" />
    <meta property="og:image:height" content="735" />
    <meta property="og:image:width" content="1400" />
    <meta property="og:image" content="https://search.censys.io/static/img/censys-mark-2025.png" />
    <meta property="og:image:height" content="512" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image" content="https://search.censys.io/static/img/censys-2025.png" />
    <meta property="og:image:height" content="162" />
    <meta property="og:image:width" content="750" />
    <meta property="og:image" content="https://search.censys.io/static/img/censys-mark-2025.png" />
    <meta property="og:image:height" content="180" />
    <meta property="og:image:width" content="180" />
  
  
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:site" content="@censysio" />
    <meta name="twitter:image" content="https://search.censys.io/static/img/censys-mark-2025.png" />
    <meta name="twitter:image:alt" content="Censys Logo" />
  
  
<!-- Open Search -->
<link rel="search" href="https://search.censys.io/static/xml/search.xml" type="application/opensearchdescription+xml" title="Censys">

  
  
  
  <!-- Favicons -->
  <link rel="shortcut icon" href="https://search.censys.io/static/img/favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" sizes="180x180" href="https://search.censys.io/static/img/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="https://search.censys.io/static/img/favicon-2025-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="https://search.censys.io/static/img/favicon-2025-16x16.png">
  <link rel="icon" href="https://search.censys.io/static/img/censys-mark-2025.png" />
  <link rel="manifest" href="https://search.censys.io/static/site.webmanifest">
  <link rel="mask-icon" href="https://search.censys.io/static/img/safari-pinned-tab.svg" color="#ff7a37">
  <meta name="msapplication-TileColor" content="#ff7a37">
  <meta name="msapplication-TileImage" content="https://search.censys.io/static/img/mstile-150x150.png">
  <meta name="theme-color" content="#ffffff">

  
  <!-- CSS Files -->
  <link rel="stylesheet" charset="utf-8" href="https://search.censys.io/static/css/bootstrap.min.css" />
  <link rel="stylesheet" charset="utf-8" href="https://search.censys.io/static/css/material-kit.min.css" />
  <link rel="stylesheet" charset="utf-8" href="https://search.censys.io/static/css/custom.css?version=5d6f39b8be33734e21278576a9f368cd" />

  <link rel="stylesheet" charset="utf-8" href="https://search.censys.io/static/css/marketing/footer.css" />
  <link rel="stylesheet" charset="utf-8" href="https://search.censys.io/static/css/app/footer-light.css" />
  
  

  <!-- Fonts and icons -->
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&family=Roboto+Mono:wght@400;500&family=Roboto:wght@400;500;700&family=Titillium+Web:wght@400;700&family=Material+Icons&display=swap">
  <link rel="preload" as="font" type="font/woff2" crossorigin href="https://unpkg.com/font-awesome@4.7.0/fonts/fontawesome-webfont.woff2?v=4.7.0"/>
  <link rel="preload" as="style" href="https://unpkg.com/font-awesome@4.7.0/css/font-awesome.min.css" onload="this.rel='stylesheet'"/><link rel="stylesheet" href="https://unpkg.com/@highlightjs/cdn-assets@11.4.0/styles/github.min.css">
  <script src="https://unpkg.com/jquery@3.6.0/dist/jquery.min.js"></script>
  <script>
    window.jQuery || document.write('<script src="https://search.censys.io/static/js/jquery.min.js"><\/script>');
  </script>
</head>
<body >

  
  <nav class="NavBar NavBar--fixed-top navbar-fixed-top">
    <div class="container NavBar__container">
      
      <a href="/">
        <img class="NavBar__logo logo"
             src="https://search.censys.io/static/img/censys-2025.svg" alt="Censys" title="logo">
        <img class="NavBar__logo logo logo-square"
             src="https://search.censys.io/static/img/censys-mark-2025.svg" alt="Censys" title="logo">
      </a>
      

      
        
  
  
    
  
  

  <div class="NavBar__search-boxes-both">
    <form id="search" class="NavBar__search-form" method="get" action="/search" spellcheck="false">
      <div class="dropdown" id="index-picker">
        <!-- Dropdown Button with Resource Names -->
        <button id="NavBar__search-index-picker" class="NavBar__search-index-picker focus-outline" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false" aria-label="Resource Type Dropdown" type="button">
          <span class="hide-on-mobile"><i class="fa fa-fw fa-search" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
            <span id="resource-hosts" class="hide-on-mobile resource-button">Hosts</span>
            <span class="hide-on-desktop"><i class="fa fa-fw fa-desktop" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
            <span id="resource-certs" class="hide-on-mobile resource-button" hidden>Certificates</span>
            <span class="hide-on-desktop" hidden><i class="fa fa-fw fa-certificate" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
          <span class="hide-on-mobile"><i class="fa fa-fw fa-angle-down" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
        </button>
        <!-- Dropdown Content -->
        <ul class="DropdownMenu dropdown-menu pull-left" aria-labelledby="NavBar__search-index-picker">
          <li class="DropdownMenu__item DropdownMenu__item--no-link">
            <div class="DropdownMenu__header">
              Select Dataset
            </div>
          </li>
          <li class="DropdownMenu__item">
            <a id="dropdown-host" href="#">
              <span class="DropdownMenu__item-icon"><i class="fa fa-fw fa-desktop" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
              Hosts
            </a>
          </li>
          <li class="DropdownMenu__item">
            <a id="dropdown-certificates" href="#">
              <span class="DropdownMenu__item-icon"><i class="fa fa-fw fa-certificate" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i></span>
              Certificates
            </a>
          </li>
        </ul>
      </div>
      <input name="resource" id="resource" aria-label="resource" value="hosts" hidden>




<div class="dropdown" id="search-settings">
  <button id="search-settings-dropdown-menu" class="NavBar__button NavBar__button_border focus-outline" data-toggle="dropdown" aria-label="Search Settings" aria-haspopup="true" aria-expanded="false" type="button">
    <i class="fa fa-fw fa-cog" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i>
  </button>
  <div>
    <ul class="DropdownMenu dropdown-menu">
      <li class="DropdownMenu__item DropdownMenu__item--no-link">
        <div class="DropdownMenu__header">
          Settings for Search Results
        </div>
      </li>
      <li role="separator" class="DropdownMenu__divider"></li>
      <li class="DropdownMenu__item DropdownMenu__item--no-link setting">
        <div class="setting-label">Sort Order:</div>
        <div class="btn-group btn-group-xs setting-values focus-outline">
          <input type="radio" class="btn-check" name="sort" value="RELEVANCE" id="sort_order_rev" autocomplete="off" checked>
          <label class="btn btn-white" for="sort_order_rev">Relevance</label>

          <input tabindex="0" type="radio" class="btn-check" name="sort" value="ASCENDING" id="sort_order_asc" autocomplete="off" >
          <label class="btn btn-white" for="sort_order_asc">Ascending</label>

          <input tabindex="0" type="radio" class="btn-check" name="sort" value="DESCENDING" id="sort_order_desc" autocomplete="off" >
          <label class="btn btn-white" for="sort_order_desc">Descending</label>

          
        </div>
      </li>
     <li class="DropdownMenu__item DropdownMenu__item--no-link setting">
        <div class="setting-label">Per Page:</div>
        <div class="btn-group btn-group-xs setting-values focus-outline" role="group" aria-label="Results Per Page">
          <input type="radio" class="btn-check" name="per_page" value="25" id="per_page_25" autocomplete="off" checked>
          <label class="btn btn-white" for="per_page_25">25</label>

          <input type="radio" class="btn-check" name="per_page" value="50" id="per_page_50" autocomplete="off" >
          <label class="btn btn-white" for="per_page_50">50</label>

          <input type="radio" class="btn-check" name="per_page" value="100" id="per_page_100" autocomplete="off" >
          <label class="btn  btn-white" for="per_page_100">100</label>
        </div>
      </li>
      <li class="DropdownMenu__item DropdownMenu__item--no-link setting">
        <div class="setting-label">Virtual Hosts:<a href="https://support.censys.io/hc/en-us/articles/4411773845524" target="_blank" rel="noopener"><i class="fa fa-fw fa-question-circle" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom" title="Identified by both a name and an IP address.<br><small><strong>Click to learn more.</strong></small>" ></i></a></div>
        <div class="btn-group btn-group-xs setting-values focus-outline" role="group" aria-label="Options for Virtual Hosts">
          <input type="radio" class="btn-check" name="virtual_hosts" value="EXCLUDE" id="virtual_hosts_exclude" autocomplete="off" checked>
          <label class="btn btn-white" for="virtual_hosts_exclude">Exclude</label>

          <input type="radio" class="btn-check" name="virtual_hosts" value="INCLUDE" id="virtual_hosts_include" autocomplete="off" >
          <label class="btn btn-white" for="virtual_hosts_include">Include</label>

          <input type="radio" class="btn-check" name="virtual_hosts" value="ONLY" id="virtual_hosts_only" autocomplete="off" >
          <label class="btn  btn-white" for="virtual_hosts_only">Only</label>
        </div>
      </li>
    
    </ul>
  </div>
</div>
<input class="NavBar__search-box" type="text" placeholder="Search" aria-label="Search" name="q" id="q"
              value="" autocomplete="off"
              
              onfocus="this.selectionStart = this.selectionEnd = this.value.length;">
      <button class="NavBar__search-clear focus-outline" id="clear-query-button" type="button" aria-label="Clear Query">
        <i class="fa fa-fw fa-close" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i>
      </button>
      <button class="NavBar__search-multiline-toggle focus-outline" id="multiline-toggle" type="button" aria-label="Expand">
        <i class="fa fa-fw fa-expand" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  id="expand-icon"></i>
      </button>
      

<div class="dropdown" id="export-query">
  <!-- add an onclick to button -->
  <button id="export-query-dropdown-menu" class="NavBar__button NavBar__button_border focus-outline" data-toggle="dropdown" aria-label="Export Query" aria-haspopup="true" aria-expanded="false" type="button">
    <i class="fa fa-fw fa-terminal" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i>
  </button>
  <div>
    <ul class="DropdownMenu dropdown-menu dropdown-menu-right">
      <li class="DropdownMenu__item DropdownMenu__item--no-link">
        <div class="DropdownMenu__header">
          Export Query as cURL
        <button class="NavBar__search-copy focus-outline" id="copy-query-button" type="button" aria-label="Copy Query" onclick="copyToClipboard('copy-text')">
          <i class="fa fa-fw fa-clipboard" aria-hidden="true" data-html="true" data-toggle="tooltip" data-placement="bottom"  ></i>
        </button>
        </div>
      </li>
      <li role="separator" class="DropdownMenu__divider"></li>
      <li class="DropdownMenu__item DropdownMenu__item--no-link">
        <div class="DropdownMenu__header">
        </div>
        <div class="DropdownMenu__inner-text-box">
          <div class="copy-text">
            <pre id=copy-text> </pre>
          </div>
        </div>
        <div class="DropdownMenu__link">
          <a href="/account/api"> Get API Credentials </a>
        </div>
      </li>
    </ul>
  </div>

  <script>
  function copyToClipboard(containerid) {
    var textToCopy = document.getElementById(containerid).innerText;
    navigator.clipboard.writeText(textToCopy);
  }
  </script>

  <script>
    /* converts to api */
    var params = new URLSearchParams(window.location.search);

    function htmlEncode(s) {
      return s.replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/'/g, '&quot;')
        .replace(/"/g, '&#34;');
    }

    function getV2ApiCurlString(apiResource) {
      var apiParams = new URLSearchParams();

      if (params.get("per_page") !== null) {
        apiParams.set("per_page", params.get("per_page"));
      }
      if (params.get("virtual_hosts") !== null) {
        apiParams.set("virtual_hosts", params.get("virtual_hosts"));
      }
      if (params.get("sort") !== null) {
        apiParams.set("sort", params.get("sort"));
      }
      if (params.get("cursor") !== null) {
        apiParams.set("cursor", params.get("cursor"));
      }
      apiParams.set("q", params.get("q", ""));

      exportString = "curl -g -X 'GET' \\<br>";
      exportString += "'https://search.censys.io/api/v2/" + apiResource + "/search?";
      exportString += apiParams.toString() + "' \\<br>";
      exportString += "-H 'Accept: application/json' \\<br>";
      exportString += "--user \"$CENSYS_API_ID:$CENSYS_API_SECRET\"";
      return exportString
    }

    if (params.get("resource") === "hosts") {
      document.getElementById('copy-text').innerHTML = getV2ApiCurlString("hosts");
    }
    else if (params.get("resource") === "certificates") {
      document.getElementById('copy-text').innerHTML = getV2ApiCurlString("certificates");
    }
  </script>

  <script type="text/javascript">
  /* hides button if no query */
    var url = window.location.href;
    var msg = document.getElementById('export-query-dropdown-menu');
    /* update to check for resource=hosts and certs instead of length */
    if( url.search("resource=hosts") != -1 ||
      url.search("resource=certificates") != -1 )  {
        msg.style.display = "block";
    }
    else {
      msg.style.display = "none";
    }
  </script>

</div>

      <input class="NavBar__search-submit focus-outline" id="submit-button" type="submit" value="Search">
    </form>

    <div class="hide-on-mobile hide-on-desktop" id="msf">
      <textarea rows="1" class="NavBar__search-box-multi"
        placeholder="Press enter for more lines; Shift+Enter executes the search from this box."
        aria-label="Search" name="qm" id="qm"
        autocomplete="off" onfocus="this.selectionStart = this.selectionEnd = this.value.length;"
        >
        
        </textarea>
    </div>
  </div>

      <div class="NavBar__unauthenticated-actions">
          <a class="NavBar__register-link" href="https://search.censys.io/register">Register</a>
          <a class="NavBar__login-link" href="https://censys.io/login?came_from=https%3A%2F%2Fsearch.censys.io&amp;from_censys_owned_external=True"
             onClick="window.location.href = 'https://censys.io/login?from_censys_owned_external=True&came_from=' + encodeURIComponent(window.location); return false;">Log In</a>
        </div></div>
    
  </nav>

    <div id="content">
        
  <div class='search-bar query-below-blue-bar query'>
  </div> <!-- /search-bar -->

  <div>
    
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        
          












        
      </div>
    </div>
    
<div class="page">
<h1>Censys Error</h1>
<br/>
<h4><b>Error 404.</b> The requested URL was not found. If you believe this is an error on our part, please <a href="/cdn-cgi/l/email-protection#8cfff9fcfce3fef8ccefe9e2fff5ffa2e5e3">contact us</a>.</h4>


</div>

  </div>

  </div> <!-- /belowsearchbarcontent -->

    </div>
    

<footer>
  <div class="footer__container container">
    <div class="row">
      <div class="col-md-6 left">
        <span class="part">
          <a href="https://censys.com/resources/" target="_blank" rel="noopener">Resource Hub</a>
        </span>
        •
        <span class="part">
          <a href="https://censys.com/attack-surface-management/" target="_blank" rel="noopener">Attack Surface Management</a>
        </span>
        •
        <span class="part">
          <a href="https://censys.com/federal/" target="_blank" rel="noopener">Government</a>
        </span>
        •
        <span class="part">
          <a href="https://support.censys.io/hc/en-us/articles/360038761891-Research-Access-to-Censys-Data" target="_blank"
            rel="noopener">Research Access</a>
        </span>
      </div>
      <div class="col-md-6 right">
        <span class="part">
          Need Help?
          <a href="https://support.censys.io/" target="_blank" rel="noopener">Help Center</a>
          or
          <a href="/cdn-cgi/l/email-protection#671412171708151327040209141e14490e08"><span class="__cf_email__" data-cfemail="b7c4c2c7c7d8c5c3f7d4d2d9c4cec499ded8">[email&#160;protected]</span></a>
        </span>
        |
        <span class="part">
            &copy; 2025 Censys
        </span>
      </div>
    </div>
  </div>
</footer>

    
    <!-- Scripts -->




















    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script charset="utf-8" src="https://search.censys.io/static/js/bootstrap.min.js"></script>
    <script src="https://search.censys.io/static/js/material.min.js"></script>
    <script src="https://search.censys.io/static/js/material-kit.min.js" type="text/javascript"></script>
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.4.0/highlight.min.js"></script>
    <script>
    // If hljs is defined
    if (typeof hljs !== 'undefined') {
      // Highlight all code blocks
      hljs.highlightAll();
    }
    </script>
    <script>
    $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();

        $('#quota-alert').on('closed.bs.alert', function () {
            $.post("https://search.censys.io/account/_dismiss_quota_alert",
                {"csrf_token": "954c40dc23f22319120078c6f6b9a0d47f49fb2c"},
                function (result) { }
            );
        });
    });
    </script>

    
  
  
    
  
  

  <script>
    // Fixes Settings modal so that clicking options doesn't automatically close it.
    $(document).on('click', '#search-settings .dropdown-menu', function (e) {
      e.stopPropagation();
    });
    $(document).on('click', '#export-query .dropdown-menu-right', function (e) {
      e.stopPropagation();
    });

    // Event handler for closing the index picker dropdown when tab
    // focus moves away from the dropdown or the dropdown button
    $(document).on('blur', '#index-picker', function (e) {
      var currentFocus = e.relatedTarget;
      var newFocusContained = $.contains(document.getElementById('index-picker'), currentFocus)
      var indexPickerOpen = $("#index-picker").hasClass('open')

      // Focus will be null if the menu is being navigated by mouse cursor.
      // We don't want to automatically close the menu in such case
      if (currentFocus !== null && !newFocusContained && indexPickerOpen) {
        // If we're focusing away from the dropdown and the dropdown is
        // opened, then close the dropdown by toggling the state
        $("#NavBar__search-index-picker").dropdown('toggle');
      }
    });

    // Event handler for closing the search settings dropdown when tab
    // focus moves away from the dropdown or the dropdown button
    $(document).on('blur', '#search-settings', function (e) {
      var currentFocus = e.relatedTarget;
      var newFocusContained = $.contains(document.getElementById('search-settings'), currentFocus)
      var searchSettingsOpen = $("#search-settings").hasClass('open')

      // Focus will be null if the menu is being navigated by mouse cursor.
      // We don't want to automatically close the menu in such case
      if (currentFocus !== null && !newFocusContained && searchSettingsOpen) {
        // If we're focusing away from the dropdown and the dropdown is
        // opened, then close the dropdown by toggling the state
        $("#search-settings-dropdown-menu").dropdown('toggle');
      }
    });

    // Event handler for closing the query export dropdown when tab
    // focus moves away from the dropdown or the dropdown button
    $(document).on('blur', '#export-query', function (e) {
      var currentFocus = e.relatedTarget;
      var newFocusContained = $.contains(document.getElementById('export-query'), currentFocus)
      var exportQueryOpen = $("#export-query").hasClass('open')

      // Focus will be null if the menu is being navigated by mouse cursor.
      // We don't want to automatically close the menu in such case
      if (currentFocus !== null && !newFocusContained && exportQueryOpen) {
        // If we're focusing away from the dropdown and the dropdown is
        // opened, then close the dropdown by toggling the state
        $("#export-query-dropdown-menu").dropdown('toggle');
      }
    });

    $("#search").submit(function(e) {
      let cacheOff = $("#bypass_cache_off");
      if (cacheOff.length > 0 && cacheOff.is(":checked")) {
        cacheOff.attr("disabled", true);
      }
    });

    function updateMainFromMulti() {
      // View other icons: https://stackoverflow.com/a/18931703/6909734
      $("#q").val($("#qm").val().replace(/\n/g, " \u21A9 "));
    }

    $("#qm").keydown(function(event) {
      if (event.keyCode == 13 && event.shiftKey) {
        event.preventDefault();
        $("#search").submit();
      }
    });

    $("#qm").keyup(function() {
      updateMainFromMulti();
    });

    $("#q").focus(function(e) {
      // Hide expanded form when search bar is clicked
      var multiSearchForm = $("#msf");
      if (!multiSearchForm.hasClass("hide-on-mobile hide-on-desktop")) {
        $("#multiline-toggle").click()
      }
    });

    $("#clear-query-button").click(function(e) {
      e.preventDefault();
      $("#q").val("");
      $("#qm").val("");
      $("#q").focus();
    });

    $("#multiline-toggle").click(function(e) {
      e.preventDefault();
      var multiSearchForm = $("#msf");
      var multiToggleButton = $("#multiline-toggle");
      var mainSearchBar = $("#q");
      var multiSearchBar = $("#qm");
      var expandIcon = $("#expand-icon");
      if (multiSearchForm.hasClass("hide-on-mobile hide-on-desktop")) {
          // do expand
          expandIcon.attr("class", "fa fa-compress");
          multiSearchForm.attr("class", "NavBar__multi-search-form");
          multiToggleButton.attr("class", "NavBar__search-multiline-toggle-enabled focus-outline");
          mainSearchBar.attr("readonly", true);
          mainSearchBar.css("color", "#cbcbcb");
          var mainQuery = mainSearchBar.val();
          if (multiSearchBar.val() !== mainQuery || mainQuery.includes(" \u21A9 ")) {
              multiSearchBar.val(mainQuery.replaceAll(" \u21A9 ", "\r\n"));
          }
          multiSearchBar.focus();
          // If there aren't autocomplete suggestions,
          // the textarea won't expand right away without this !
          multiSearchBar.css("height", "50px");
      } else {
          // do shrink
          expandIcon.attr("class", "fa fa-expand");
          multiSearchForm.attr("class", "hide-on-mobile hide-on-desktop");
          multiToggleButton.attr("class", "NavBar__search-multiline-toggle focus-outline");
          mainSearchBar.attr("readonly", false);
          mainSearchBar.css("color", "black");
          mainSearchBar.focus();
      }
    });

    // Grow size of multiline box dynamically
    function textAreaAdjust(element){
      // This redundant assignment is necessary
      // or the input won't shrink when removing lines of text.
      // -- minimum size of 50 to make it clear the box can expand.
      element.style.height = "50px";
      element.style.height = Math.min(200, element.scrollHeight)+"px";
    }

    function createRedirectURL() {
      var url = new URL(window.location.href);
      var params = new URLSearchParams(url.search);
      var resource = $('#resource').val()
      params.set("resource", resource);
      params.delete("cursor");

      // Handle redirects AWAY from legacy certs pages
      if (url.pathname.includes('/certificates-legacy')) {
        if (url.pathname.includes('definitions')) {
          url.pathname = '/search/definitions'
        } else if (url.pathname.includes('examples')) {
          url.pathname = '/search/examples'
        } else if (url.pathname.includes('help')) {
          url.pathname = '/search/language'
        } else if (url.pathname.includes('report')) {
          url.pathname = 'search/report'
        } else {
          url.pathname = '/search'
          if (!params.has("q")) {
            params.set("q", $('#q').val())
          }
        }
      // Handle redirect TO legacy certificates pages
      } else if (resource === 'certificates-legacy') {
        if (url.pathname.includes('definitions')) {
          url.pathname = '/certificates-legacy/help/definitions'
        } else if (url.pathname.includes('examples')) {
          url.pathname = '/certificates-legacy/help/examples'
        } else if (url.pathname.includes('language')) {
          url.pathname = '/certificates-legacy/help'
        } else if (url.pathname.includes('report')) {
          url.pathname = '/certificates-legacy/report'
        } else {
          url.pathname = '/certificates-legacy' // results
          if (!params.has("q")) {
            params.set("q", $('#q').val())
          }
        }
      }

      if (url.pathname.includes('/certificates/') || url.pathname.includes('/hosts/')) {
        url.pathname = '/search'
        if (!params.has("q")) {
          params.set("q", $('#q').val())
        }
      }

      url.search = params.toString();
      return url;
    }

    $('#dropdown-host').click(function(e) {
      $('#resource').attr('value', 'hosts');
      
          var newURL = createRedirectURL()
          window.location.replace(newURL.toString());
      
    })


    $('#dropdown-certificates-legacy').click(function(e) {
      $('#resource').attr('value', 'certificates-legacy');
      
        var newURL = createRedirectURL()
        window.location.replace(newURL.toString());
      
    })

    $('#dropdown-certificates').click(function(e) {
      $('#resource').attr('value', 'certificates');
      
        var newURL = createRedirectURL()
        window.location.replace(newURL.toString());
      
    })
  </script>

  
<script charset="utf-8" src="https://search.censys.io/static/js/jquery.autocomplete.min.js"></script>

<script>
  function logicSuggestion(statement) {
    return {
      data: {
        category: "<i class=\"fa fa-fw fa-toggle-on\" aria-hidden=\"true\"></i> Boolean Logic",
      },
      value: statement,
    };
  }

  function serviceNameSuggestion(serviceName) {
    return {
      data: {
        category: "<i class=\"fa fa-fw fa-server\" aria-hidden=\"true\"></i> Service Name",
      },
      value: serviceName,
    };
  }

  function _lookupFilter(suggestion, originalQuery, queryLowerCase) {
    return suggestion.value.toLowerCase().indexOf(queryLowerCase) !== -1;
  };

  var serviceNameSuggestions = $.map(["HTTP", "SSH", "SMTP", "FTP", "DNS"], serviceNameSuggestion);
  var logicSuggestions = $.map(["and", "or"], logicSuggestion);
  var urls = {
    "hosts": "https://search.censys.io/static/data/autocomplete-hosts.json?version=925fc5168bb6338c8d0cb8e57bd8cd88",
    "certificates-legacy": "https://search.censys.io/static/data/autocomplete-certificates.json?version=e7defab6f2a77da0b9230d823fb97ae7",
  };

  var fieldSuggestions = {};
  var resource = $('#resource').val()
  var resources = [
    'certificates-legacy',
    'hosts',
  ]

  resources.forEach(function(val, index) {
    $.ajax({
      url: urls[val],
      success: function (data) {
        if (data === undefined) {
          return;
        }
        fieldSuggestions[val] = data.data;
      }
    });
  })

  function beforeRenderCallback(elemId, isMultiline) {
    return function (_, suggestions) {
      if (isMultiline) {
        // Adjust size before displaying autcomplete, or
        // Suggestions will sometimes be offset
        textAreaAdjust(this);
      }
      if (suggestions) {
        $(elemId).css("border-bottom-left-radius", "0pt");
      }
    }
  }

  function onHideCallback(elemId) {
    return function (_) {
      $(elemId).css("border-bottom-left-radius", "4pt");
    }
  }

  function onSelectCallback(isMultiline) {
    return function (_) {
      this.focus();
      this.value = this.value;

      if (isMultiline) {
        updateMainFromMulti();
      }
    }
  }

  function BindAutocomplete() {
    var resource = $('#resource').val()

    var opts = {
      serviceUrl: "/api/ui/suggestions",
      params: {resource: resource},
      deferRequestBy: 80,
    };
    
    $("#q").focus();

    $("#q").autocomplete({
      ...opts,
      maxHeight: 600,
      tabDisabled: true,
      delimiter: /:|\s/gmi,
      beforeRender: beforeRenderCallback("#q", false),
      onHide:       onHideCallback("#q"),
      onSelect:     onSelectCallback(false),
    });

    $("#qm").autocomplete({
      ...opts,
      maxHeight: 600,
      tabDisabled: true,
      delimiter: /:|\s/gmi,
      beforeRender: beforeRenderCallback("#qm", true),
      onHide:       onHideCallback("#qm"),
      onSelect:     onSelectCallback(true),
    });
  }
  
  // Create click handlers to re-bind the autocompelete options
  // When a new resource type is selected
  $('#dropdown-host').click(function(e) {
    BindAutocomplete();
  });

  $('#dropdown-certificates').click(function(e) {
    BindAutocomplete();
  });
  
  // Bind the autocomplete to the initial page state
  BindAutocomplete();
</script>





    
    

    


<script async src="https://www.googletagmanager.com/gtag/js?id=GTM-NT95GRXZ"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('gtm.start', new Date());
  gtag('config', 'GTM-NT95GRXZ')
  
</script>
  <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9684d4056fd967d9',t:'MTc1NDA0NTk4OC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>