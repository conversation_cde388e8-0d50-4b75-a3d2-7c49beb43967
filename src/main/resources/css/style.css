/* 根样式 */
.root {
    -fx-font-family: "System", "Arial", "Microsoft YaHei", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #f5f5f5;
}

/* 标题样式 */
.title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.subtitle {
    -fx-text-fill: #7f8c8d;
    -fx-font-size: 14px;
}

.section-title {
    -fx-text-fill: #34495e;
    -fx-font-weight: bold;
}

/* 区域样式 */
.header-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0 0 1 0;
}

.input-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

.results-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-padding: 15;
}

.log-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1 0 0 0;
}

/* 按钮样式 */
.button {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #d5dbdb;
    -fx-border-color: #95a5a6;
}

.button:pressed {
    -fx-background-color: #bdc3c7;
}

.button:disabled {
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

.primary-button {
    -fx-background-color: #3498db;
    -fx-border-color: #2980b9;
    -fx-text-fill: white;
}

.primary-button:hover {
    -fx-background-color: #2980b9;
    -fx-border-color: #21618c;
}

.primary-button:pressed {
    -fx-background-color: #21618c;
}

/* 输入框样式 */
.text-field {
    -fx-background-color: #ffffff;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
    -fx-padding: 5;
}

.text-field:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

.text-area {
    -fx-background-color: #ffffff;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.text-area:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

.text-area .content {
    -fx-background-color: #ffffff;
}

/* 表格样式 */
.table-view {
    -fx-background-color: #ffffff;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.table-view .column-header-background {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 0 1 0;
}

.table-view .column-header {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 0 1 0 0;
}

.table-view .table-cell {
    -fx-border-color: #e8e8e8;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 5;
}

.table-row-cell:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.table-row-cell:selected .table-cell {
    -fx-text-fill: white;
}

.table-row-cell:hover {
    -fx-background-color: #ecf0f1;
}

/* 进度条样式 */
.progress-bar {
    -fx-background-color: #ecf0f1;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.progress-bar .bar {
    -fx-background-color: #3498db;
    -fx-background-radius: 2;
    -fx-padding: 0.25em;
}

/* 复选框样式 */
.check-box {
    -fx-text-fill: #2c3e50;
}

.check-box .box {
    -fx-background-color: #ffffff;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
}

.check-box:selected .mark {
    -fx-background-color: #3498db;
}

.check-box:selected .box {
    -fx-border-color: #3498db;
}

/* 标签样式 */
.label {
    -fx-text-fill: #2c3e50;
}

/* 分隔符样式 */
.separator .line {
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1 0 0 0;
}

/* 滚动条样式 */
.scroll-bar {
    -fx-background-color: #ecf0f1;
}

.scroll-bar .thumb {
    -fx-background-color: #bdc3c7;
    -fx-background-radius: 5;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #95a5a6;
}

.scroll-bar .thumb:pressed {
    -fx-background-color: #7f8c8d;
}

/* 工具提示样式 */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-background-radius: 3;
    -fx-font-size: 12px;
    -fx-padding: 5;
}
