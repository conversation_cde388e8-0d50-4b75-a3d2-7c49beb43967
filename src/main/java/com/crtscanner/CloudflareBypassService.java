package com.crtscanner;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Cloudflare绕过服务
 * 使用第三方API获取Cookie来绕过Cloudflare保护
 */
public class CloudflareBypassService {
    private static final String API_URL = "http://api-cf.zjdanli.com/cloudflare/getCookie";
    private static final String APP_ID = "71gcowhtptnexn8c3dqx0kphsnt7snpv";
    
    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public CloudflareBypassService() {
        this.httpClient = HttpClients.createDefault();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 获取指定域名的Cloudflare Cookie
     * 
     * @param host 目标域名
     * @return Cookie字符串，失败时返回null
     */
    public String getCookie(String host) {
        try {
            System.out.println("🔄 正在获取 " + host + " 的Cloudflare Cookie...");
            
            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("appid", APP_ID);
            requestBody.put("host", host);
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            
            // 创建POST请求
            HttpPost request = new HttpPost(API_URL);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            request.setEntity(new StringEntity(jsonBody, "UTF-8"));
            
            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();
                
                if (statusCode != 200) {
                    System.err.println("❌ API请求失败，状态码: " + statusCode);
                    return null;
                }
                
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity);
                
                // 解析响应
                JsonNode responseJson = objectMapper.readTree(responseBody);
                
                // 检查响应状态
                if (responseJson.has("code")) {
                    int code = responseJson.get("code").asInt();
                    if (code == 200 && responseJson.has("data")) {
                        JsonNode data = responseJson.get("data");
                        if (data.has("cookie")) {
                            String cookie = data.get("cookie").asText();
                            System.out.println("✅ 成功获取Cookie: " + cookie.substring(0, Math.min(50, cookie.length())) + "...");
                            return cookie;
                        }
                    } else {
                        String message = responseJson.has("message") ? 
                            responseJson.get("message").asText() : "未知错误";
                        System.err.println("❌ API返回错误: " + message);
                    }
                } else {
                    System.err.println("❌ API响应格式错误: " + responseBody);
                }
                
            }
            
        } catch (Exception e) {
            System.err.println("❌ 获取Cookie时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 获取Censys平台的Cookie
     *
     * @return Censys Cookie字符串
     */
    public String getCensysCookie() {
        return getCookie("censys.io");
    }
    
    /**
     * 测试API连接
     * 
     * @return 是否连接成功
     */
    public boolean testConnection() {
        try {
            System.out.println("🔍 测试API连接...");
            String testCookie = getCookie("example.com");
            boolean success = testCookie != null && !testCookie.isEmpty();
            
            if (success) {
                System.out.println("✅ API连接测试成功");
            } else {
                System.out.println("❌ API连接测试失败");
            }
            
            return success;
            
        } catch (Exception e) {
            System.err.println("❌ API连接测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }
}
