package com.crtscan.gui;

import com.crtscan.model.ScanResult;
import com.crtscan.service.CertificateScanner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 主界面窗口
 */
public class MainFrame extends JFrame {
    
    private static final Logger logger = LoggerFactory.getLogger(MainFrame.class);
    
    // UI组件
    private JTextField domainField;
    private JTextArea cookieArea;
    private JButton scanButton;
    private JButton exportButton;
    private JButton clearButton;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    private JLabel resultCountLabel;
    private JTable resultsTable;
    private DefaultTableModel tableModel;
    private JTextArea logArea;
    private JCheckBox headlessCheckBox;
    private JCheckBox verboseCheckBox;
    
    // 业务组件
    private CertificateScanner scanner;
    private ScanResult currentResult;
    
    public MainFrame() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        // 初始化扫描器
        scanner = new CertificateScanner();
        
        logger.info("主界面初始化完成");
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setTitle("CRT证书扫描工具 v1.0.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // 输入组件
        domainField = new JTextField(30);
        domainField.setToolTipText("请输入域名，例如: mi.com");
        
        cookieArea = new JTextArea(3, 50);
        cookieArea.setLineWrap(true);
        cookieArea.setWrapStyleWord(true);
        cookieArea.setToolTipText("请输入Censys平台的cookie");
        
        // 按钮
        scanButton = new JButton("开始扫描");
        exportButton = new JButton("导出结果");
        clearButton = new JButton("清除结果");
        exportButton.setEnabled(false);
        
        // 进度和状态
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setVisible(false);
        
        statusLabel = new JLabel("就绪");
        resultCountLabel = new JLabel("结果: 0");
        
        // 复选框
        headlessCheckBox = new JCheckBox("无头模式", true);
        verboseCheckBox = new JCheckBox("详细日志", true);
        
        // 表格
        String[] columnNames = {"序号", "域名/IP", "类型"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.getColumnModel().getColumn(0).setPreferredWidth(60);
        resultsTable.getColumnModel().getColumn(1).setPreferredWidth(400);
        resultsTable.getColumnModel().getColumn(2).setPreferredWidth(100);
        
        // 日志区域
        logArea = new JTextArea(6, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 顶部面板 - 标题
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        JLabel titleLabel = new JLabel("CRT证书扫描工具");
        titleLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 24));
        titlePanel.add(titleLabel);
        
        JLabel subtitleLabel = new JLabel("查找使用相同证书的所有网站");
        subtitleLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 14));
        titlePanel.add(subtitleLabel);
        
        add(titlePanel, BorderLayout.NORTH);
        
        // 中央面板
        JPanel centerPanel = new JPanel(new BorderLayout(10, 10));
        centerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 输入面板
        JPanel inputPanel = createInputPanel();
        centerPanel.add(inputPanel, BorderLayout.NORTH);
        
        // 结果面板
        JPanel resultsPanel = createResultsPanel();
        centerPanel.add(resultsPanel, BorderLayout.CENTER);
        
        add(centerPanel, BorderLayout.CENTER);
        
        // 底部面板 - 日志
        JPanel logPanel = createLogPanel();
        add(logPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建输入面板
     */
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("输入参数"));
        GridBagConstraints gbc = new GridBagConstraints();
        
        // 域名输入
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("目标域名:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(domainField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(headlessCheckBox, gbc);
        
        gbc.gridx = 3;
        panel.add(verboseCheckBox, gbc);
        
        // Cookie输入
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.NORTHWEST;
        panel.add(new JLabel("Censys Cookie:"), gbc);
        
        gbc.gridx = 1; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 0.3;
        JScrollPane cookieScroll = new JScrollPane(cookieArea);
        panel.add(cookieScroll, gbc);
        
        // 按钮和状态
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0; gbc.weighty = 0;
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.add(scanButton);
        buttonPanel.add(exportButton);
        buttonPanel.add(clearButton);
        panel.add(buttonPanel, gbc);
        
        gbc.gridx = 1; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        statusPanel.add(resultCountLabel);
        panel.add(statusPanel, gbc);
        
        // 进度条和状态
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 4; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JPanel progressPanel = new JPanel(new BorderLayout());
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(statusLabel, BorderLayout.EAST);
        panel.add(progressPanel, gbc);
        
        return panel;
    }
    
    /**
     * 创建结果面板
     */
    private JPanel createResultsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("扫描结果"));
        
        JScrollPane tableScroll = new JScrollPane(resultsTable);
        tableScroll.setPreferredSize(new Dimension(0, 300));
        panel.add(tableScroll, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建日志面板
     */
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("日志信息"));
        
        JScrollPane logScroll = new JScrollPane(logArea);
        logScroll.setPreferredSize(new Dimension(0, 150));
        panel.add(logScroll, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        scanButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
        
        exportButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                exportResults();
            }
        });
        
        clearButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                clearResults();
            }
        });
        
        // 域名输入框回车事件
        domainField.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                startScan();
            }
        });
    }
    
    /**
     * 开始扫描
     */
    private void startScan() {
        String domain = domainField.getText().trim();
        String cookie = cookieArea.getText().trim();
        
        // 验证输入
        if (domain.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入要扫描的域名", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        if (cookie.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入Censys平台的cookie", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // 清除之前的结果
        clearResults();
        
        // 设置UI状态
        setUIState(true);
        
        // 在后台线程中执行扫描
        SwingWorker<ScanResult, String> worker = new SwingWorker<ScanResult, String>() {
            @Override
            protected ScanResult doInBackground() throws Exception {
                // 设置扫描器参数
                scanner.setCookie(cookie);
                scanner.setHeadless(headlessCheckBox.isSelected());
                scanner.setVerbose(verboseCheckBox.isSelected());
                
                // 设置进度回调
                scanner.setProgressCallback((message, progress) -> {
                    SwingUtilities.invokeLater(() -> {
                        statusLabel.setText(message);
                        progressBar.setValue(progress);
                    });
                });
                
                // 设置日志回调
                scanner.setLogCallback(message -> {
                    publish(message);
                });
                
                // 执行扫描
                return scanner.scanDomain(domain);
            }
            
            @Override
            protected void process(List<String> chunks) {
                for (String message : chunks) {
                    appendLog(message);
                }
            }
            
            @Override
            protected void done() {
                try {
                    currentResult = get();
                    displayResults(currentResult);
                } catch (Exception e) {
                    logger.error("扫描失败", e);
                    JOptionPane.showMessageDialog(MainFrame.this, 
                        "扫描过程中发生错误: " + e.getMessage(), 
                        "扫描失败", 
                        JOptionPane.ERROR_MESSAGE);
                } finally {
                    setUIState(false);
                    statusLabel.setText("扫描完成");
                }
            }
        };
        
        worker.execute();
    }

    /**
     * 设置UI状态
     */
    private void setUIState(boolean scanning) {
        scanButton.setEnabled(!scanning);
        domainField.setEnabled(!scanning);
        cookieArea.setEnabled(!scanning);
        headlessCheckBox.setEnabled(!scanning);
        verboseCheckBox.setEnabled(!scanning);
        progressBar.setVisible(scanning);

        if (scanning) {
            progressBar.setValue(0);
        }
    }

    /**
     * 显示扫描结果
     */
    private void displayResults(ScanResult result) {
        if (result == null) {
            return;
        }

        if (!result.isSuccess()) {
            JOptionPane.showMessageDialog(this,
                "扫描失败: " + result.getError(),
                "错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        List<ScanResult.DomainInfo> domains = result.getDomains();

        // 清空表格
        tableModel.setRowCount(0);

        // 添加结果到表格
        for (ScanResult.DomainInfo domain : domains) {
            Object[] row = {domain.getIndex(), domain.getDomain(), domain.getType()};
            tableModel.addRow(row);
        }

        resultCountLabel.setText(String.format("结果: %d", domains.size()));
        exportButton.setEnabled(!domains.isEmpty());

        // 添加结果摘要到日志
        appendLog(String.format("扫描完成! 目标域名: %s", result.getTargetDomain()));
        appendLog(String.format("证书SHA-256: %s", result.getSha256()));
        appendLog(String.format("找到 %d 个使用相同证书的域名/IP", domains.size()));
    }

    /**
     * 导出结果
     */
    private void exportResults() {
        if (currentResult == null || currentResult.getDomains().isEmpty()) {
            JOptionPane.showMessageDialog(this, "没有可导出的结果", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出扫描结果");
        fileChooser.setSelectedFile(new java.io.File("crt_scan_results_" +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt"));

        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            java.io.File file = fileChooser.getSelectedFile();
            try {
                exportToFile(file);
                JOptionPane.showMessageDialog(this,
                    "结果已导出到: " + file.getAbsolutePath(),
                    "成功",
                    JOptionPane.INFORMATION_MESSAGE);
            } catch (IOException e) {
                logger.error("导出文件失败", e);
                JOptionPane.showMessageDialog(this,
                    "导出文件失败: " + e.getMessage(),
                    "错误",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 导出到文件
     */
    private void exportToFile(java.io.File file) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("CRT证书扫描结果报告\n");
            writer.write("=".repeat(50) + "\n");
            writer.write("目标域名: " + currentResult.getTargetDomain() + "\n");
            writer.write("证书SHA-256: " + currentResult.getSha256() + "\n");
            writer.write("扫描时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("找到 " + currentResult.getDomains().size() + " 个使用相同证书的域名/IP:\n\n");

            for (int i = 0; i < currentResult.getDomains().size(); i++) {
                ScanResult.DomainInfo domain = currentResult.getDomains().get(i);
                writer.write(String.format("%3d. %s (%s)\n", i + 1, domain.getDomain(), domain.getType()));
            }
        }
    }

    /**
     * 清除结果
     */
    private void clearResults() {
        tableModel.setRowCount(0);
        logArea.setText("");
        resultCountLabel.setText("结果: 0");
        exportButton.setEnabled(false);
        currentResult = null;
    }

    /**
     * 添加日志
     */
    private void appendLog(String message) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        logArea.append(String.format("[%s] %s\n", timestamp, message));
        logArea.setCaretPosition(logArea.getDocument().getLength());
    }
}
