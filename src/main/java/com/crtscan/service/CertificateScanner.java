package com.crtscan.service;

import com.crtscan.model.ScanResult;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 证书扫描服务类
 */
public class CertificateScanner {
    
    private static final Logger logger = LoggerFactory.getLogger(CertificateScanner.class);
    
    private String cookie;
    private boolean headless = true;
    private boolean verbose = true;
    private BiConsumer<String, Integer> progressCallback;
    private Consumer<String> logCallback;
    
    private final HttpClient httpClient;
    
    public CertificateScanner() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
    }
    
    /**
     * 扫描域名证书
     */
    public ScanResult scanDomain(String domain) throws Exception {
        ScanResult result = new ScanResult(extractDomain(domain));
        
        try {
            updateProgress("正在查询证书信息...", 10);
            log("开始扫描域名: " + domain);
            
            // 步骤1: 查询crt.sh获取证书ID
            List<String> certIds = queryCrtSh(domain);
            if (certIds.isEmpty()) {
                result.setError("无法获取证书信息");
                return result;
            }
            
            result.setCertIds(certIds);
            updateProgress("获取证书SHA-256指纹...", 30);
            
            // 步骤2: 获取SHA-256指纹
            String sha256 = getCertSha256(certIds.get(0));
            if (sha256 == null && certIds.size() > 1) {
                sha256 = getCertSha256(certIds.get(certIds.size() - 1));
            }
            
            if (sha256 == null) {
                result.setError("无法获取SHA-256指纹");
                return result;
            }
            
            result.setSha256(sha256);
            updateProgress("搜索使用相同证书的域名...", 50);
            
            // 步骤3: 在Censys搜索使用相同证书的域名
            List<String> relatedDomains = searchCensysWithSelenium(sha256);
            
            // 处理结果
            for (String relatedDomain : relatedDomains) {
                String type = determineType(relatedDomain);
                result.addDomain(relatedDomain, type);
            }
            
            result.setSuccess(true);
            updateProgress("扫描完成", 100);
            log("扫描完成，找到 " + relatedDomains.size() + " 个相关域名");
            
        } catch (Exception e) {
            logger.error("扫描过程中发生错误", e);
            result.setError(e.getMessage());
            throw e;
        }
        
        return result;
    }
    
    /**
     * 查询crt.sh获取证书ID
     */
    private List<String> queryCrtSh(String domain) throws Exception {
        List<String> certIds = new ArrayList<>();
        
        for (int attempt = 0; attempt < 3; attempt++) {
            try {
                log("尝试第 " + (attempt + 1) + " 次查询crt.sh...");
                
                String url = "https://crt.sh/?q=" + URLEncoder.encode(domain, StandardCharsets.UTF_8);
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .timeout(Duration.ofSeconds(60))
                        .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    log("crt.sh响应成功，状态码: " + response.statusCode());
                    
                    Document doc = Jsoup.parse(response.body());
                    Elements links = doc.select("a[href*='?id=']");
                    
                    for (Element link : links) {
                        String href = link.attr("href");
                        if (href.contains("?id=")) {
                            String certId = href.substring(href.indexOf("?id=") + 4);
                            if (certId.matches("\\d+")) {
                                certIds.add(certId);
                            }
                        }
                    }
                    
                    if (!certIds.isEmpty()) {
                        log("找到 " + certIds.size() + " 个证书");
                        log("第一个证书ID: " + certIds.get(0));
                        log("最后一个证书ID: " + certIds.get(certIds.size() - 1));
                        return certIds;
                    }
                }
                
            } catch (Exception e) {
                log("第 " + (attempt + 1) + " 次尝试失败: " + e.getMessage());
                if (attempt < 2) {
                    Thread.sleep(5000);
                } else {
                    throw e;
                }
            }
        }
        
        return certIds;
    }
    
    /**
     * 获取证书SHA-256指纹
     */
    private String getCertSha256(String certId) throws Exception {
        for (int attempt = 0; attempt < 3; attempt++) {
            try {
                log("尝试第 " + (attempt + 1) + " 次获取证书详情...");
                
                String url = "https://crt.sh/?id=" + certId;
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .timeout(Duration.ofSeconds(60))
                        .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    log("证书详情响应成功，状态码: " + response.statusCode());
                    
                    // 查找SHA-256指纹
                    Pattern pattern = Pattern.compile("SHA-256.*?([a-fA-F0-9]{64})", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
                    Matcher matcher = pattern.matcher(response.body());
                    
                    if (matcher.find()) {
                        String sha256 = matcher.group(1).toLowerCase();
                        log("找到SHA-256指纹: " + sha256);
                        return sha256;
                    } else {
                        log("未找到证书ID " + certId + " 的SHA-256指纹");
                        return null;
                    }
                }
                
            } catch (Exception e) {
                log("第 " + (attempt + 1) + " 次尝试失败: " + e.getMessage());
                if (attempt < 2) {
                    Thread.sleep(3000);
                } else {
                    throw e;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 使用Selenium在Censys搜索
     */
    private List<String> searchCensysWithSelenium(String sha256) throws Exception {
        WebDriver driver = null;
        List<String> domains = new ArrayList<>();
        
        try {
            updateProgress("设置WebDriver...", 60);
            log("设置Chrome WebDriver...");
            
            // 设置WebDriverManager
            WebDriverManager.chromedriver().setup();
            
            // 配置Chrome选项
            ChromeOptions options = new ChromeOptions();
            if (headless) {
                options.addArguments("--headless");
            }
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.addExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
            options.addExperimentalOption("useAutomationExtension", false);
            options.addArguments("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            
            driver = new ChromeDriver(options);
            driver.executeScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");
            
            log("Chrome WebDriver设置成功");
            
            // 设置cookies
            if (cookie != null && !cookie.trim().isEmpty()) {
                updateProgress("设置cookies...", 65);
                setCookies(driver);
            }
            
            // 构建搜索URL
            String searchQuery = String.format("cert.fingerprint_sha256 = \"%s\"", sha256);
            String url = "https://platform.censys.io/search?q=" + URLEncoder.encode(searchQuery, StandardCharsets.UTF_8);
            
            updateProgress("访问Censys搜索页面...", 70);
            log("访问搜索URL: " + url);
            driver.get(url);
            
            // 等待页面加载
            Thread.sleep(5000);
            
            // 检查Cloudflare验证
            updateProgress("等待页面加载...", 75);
            if (driver.getPageSource().contains("正在验证您是否是真人") || 
                driver.getPageSource().contains("Just a moment")) {
                log("检测到Cloudflare验证，等待通过...");
                
                for (int i = 0; i < 60; i++) {
                    Thread.sleep(1000);
                    if (!driver.getPageSource().contains("正在验证您是否是真人") && 
                        !driver.getPageSource().contains("Just a moment")) {
                        log("Cloudflare验证已通过");
                        break;
                    }
                    if (i % 10 == 0) {
                        log("仍在等待Cloudflare验证... (" + (i + 1) + "/60秒)");
                    }
                }
            }
            
            // 等待搜索结果
            updateProgress("等待搜索结果...", 85);
            Thread.sleep(5000);
            
            // 提取域名
            updateProgress("提取域名信息...", 90);
            domains = extractDomainsFromPage(driver.getPageSource());
            
            log("从页面提取到 " + domains.size() + " 个有效的域名/IP");
            
        } finally {
            if (driver != null) {
                driver.quit();
            }
        }
        
        return domains;
    }

    /**
     * 设置cookies
     */
    private void setCookies(WebDriver driver) {
        log("设置cookies...");

        // 首先访问主页以设置域名
        driver.get("https://platform.censys.io/");

        // 解析并设置cookies
        String[] cookiePairs = cookie.split(";");
        for (String cookiePair : cookiePairs) {
            if (cookiePair.contains("=")) {
                String[] parts = cookiePair.trim().split("=", 2);
                if (parts.length == 2) {
                    org.openqa.selenium.Cookie seleniumCookie = new org.openqa.selenium.Cookie(parts[0], parts[1], ".censys.io", "/", null);
                    driver.manage().addCookie(seleniumCookie);
                }
            }
        }

        log("Cookies设置完成");
    }

    /**
     * 从页面提取域名
     */
    private List<String> extractDomainsFromPage(String pageSource) {
        Set<String> allNames = new HashSet<>();

        try {
            Document doc = Jsoup.parse(pageSource);

            // 使用正则表达式查找域名和IP
            Pattern domainPattern = Pattern.compile("\\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+" +
                    "[a-zA-Z]{2,}\\b");
            Pattern ipPattern = Pattern.compile("\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
                    "(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b");

            // 在整个页面中搜索
            Matcher domainMatcher = domainPattern.matcher(pageSource);
            while (domainMatcher.find()) {
                String domain = domainMatcher.group();
                if (isValidDomainOrIp(domain)) {
                    allNames.add(domain);
                }
            }

            Matcher ipMatcher = ipPattern.matcher(pageSource);
            while (ipMatcher.find()) {
                String ip = ipMatcher.group();
                if (isValidDomainOrIp(ip)) {
                    allNames.add(ip);
                }
            }

            // 过滤掉不相关的域名
            List<String> filteredNames = new ArrayList<>();
            Set<String> excludePatterns = Set.of(
                    "censys.io", "google.com", "cloudflare.com", "amazonaws.com",
                    "gstatic.com", "googleapis.com", "doubleclick.net", "googlesyndication.com",
                    "example.com", "localhost", "127.0.0.1", "0.0.0.0"
            );

            for (String name : allNames) {
                boolean shouldExclude = false;
                for (String exclude : excludePatterns) {
                    if (name.toLowerCase().contains(exclude)) {
                        shouldExclude = true;
                        break;
                    }
                }

                // 排除文件名
                if (!shouldExclude && !name.matches(".*\\.(js|css|png|jpg|gif|svg)$")) {
                    filteredNames.add(name);
                }
            }

            filteredNames.sort(String::compareToIgnoreCase);
            return filteredNames;

        } catch (Exception e) {
            logger.error("提取域名失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 验证是否为有效的域名或IP地址
     */
    private boolean isValidDomainOrIp(String text) {
        if (text == null || text.length() > 100 || text.length() < 3) {
            return false;
        }

        if (!text.contains(".")) {
            return false;
        }

        // 排除明显不是域名的文本
        if (text.matches(".*[<>\"'()\\[\\]{}\\s\\n\\t\\\\].*")) {
            return false;
        }

        String[] parts = text.split("\\.");

        // IP地址检查
        if (parts.length == 4) {
            try {
                for (String part : parts) {
                    if (!part.matches("\\d+")) {
                        break;
                    }
                    int num = Integer.parseInt(part);
                    if (num < 0 || num > 255) {
                        break;
                    }
                }
                return true;
            } catch (NumberFormatException e) {
                // 继续域名检查
            }
        }

        // 域名检查
        if (parts.length >= 2) {
            for (String part : parts) {
                if (part.isEmpty() || !part.matches("[a-zA-Z0-9-]+")) {
                    return false;
                }
                if (part.startsWith("-") || part.endsWith("-")) {
                    return false;
                }
            }
            return true;
        }

        return false;
    }

    /**
     * 确定域名类型
     */
    private String determineType(String domain) {
        if (domain.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return "IP";
        } else {
            return "域名";
        }
    }

    /**
     * 从URL中提取域名
     */
    private String extractDomain(String url) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }

        try {
            URI uri = URI.create(url);
            return uri.getHost();
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * 更新进度
     */
    private void updateProgress(String message, int progress) {
        if (progressCallback != null) {
            progressCallback.accept(message, progress);
        }
    }

    /**
     * 记录日志
     */
    private void log(String message) {
        if (verbose && logCallback != null) {
            logCallback.accept(message);
        }
        logger.info(message);
    }

    // Getters and Setters
    public String getCookie() {
        return cookie;
    }

    public void setCookie(String cookie) {
        this.cookie = cookie;
    }

    public boolean isHeadless() {
        return headless;
    }

    public void setHeadless(boolean headless) {
        this.headless = headless;
    }

    public boolean isVerbose() {
        return verbose;
    }

    public void setVerbose(boolean verbose) {
        this.verbose = verbose;
    }

    public void setProgressCallback(BiConsumer<String, Integer> progressCallback) {
        this.progressCallback = progressCallback;
    }

    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
}
