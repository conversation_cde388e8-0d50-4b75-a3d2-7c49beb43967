package com.crtscan;

import javax.swing.*;
import java.awt.*;

public class TestFrame extends JFrame {
    
    public TestFrame() {
        setTitle("测试窗口 - 验证GUI是否正常");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        // 创建面板
        JPanel panel = new JPanel(new BorderLayout());
        
        // 标题
        JLabel titleLabel = new JLabel("CRT证书扫描工具 - 测试版本", JLabel.CENTER);
        titleLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 24));
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // 中央内容
        JPanel centerPanel = new JPanel(new GridLayout(5, 2, 10, 10));
        centerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        centerPanel.add(new JLabel("域名:"));
        centerPanel.add(new JTextField("mi.com"));
        
        centerPanel.add(new JLabel("Cookie:"));
        centerPanel.add(new JTextArea(3, 30));
        
        centerPanel.add(new JLabel("选项:"));
        JPanel optionPanel = new JPanel(new FlowLayout());
        optionPanel.add(new JCheckBox("无头模式"));
        optionPanel.add(new JCheckBox("详细日志"));
        centerPanel.add(optionPanel);
        
        centerPanel.add(new JLabel("操作:"));
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(new JButton("开始扫描"));
        buttonPanel.add(new JButton("导出结果"));
        buttonPanel.add(new JButton("清除结果"));
        centerPanel.add(buttonPanel);
        
        centerPanel.add(new JLabel("状态:"));
        centerPanel.add(new JLabel("就绪"));
        
        panel.add(centerPanel, BorderLayout.CENTER);
        
        // 底部
        JTextArea logArea = new JTextArea(8, 50);
        logArea.setText("这是日志区域...\n测试GUI是否正常显示\n如果你能看到这个完整的界面，说明GUI工作正常");
        logArea.setEditable(false);
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("日志"));
        panel.add(scrollPane, BorderLayout.SOUTH);
        
        add(panel);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            TestFrame frame = new TestFrame();
            frame.setVisible(true);
        });
    }
}
