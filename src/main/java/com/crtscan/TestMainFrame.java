package com.crtscan;

import com.crtscan.gui.MainFrame;

import javax.swing.*;

public class TestMainFrame {
    public static void main(String[] args) {
        System.out.println("开始测试MainFrame...");
        
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("设置外观...");
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                
                System.out.println("创建MainFrame...");
                MainFrame frame = new MainFrame();
                
                System.out.println("显示MainFrame...");
                frame.setVisible(true);
                
                System.out.println("MainFrame创建成功！");
                
            } catch (Exception e) {
                System.err.println("创建MainFrame时发生错误:");
                e.printStackTrace();
                
                // 显示错误对话框
                JOptionPane.showMessageDialog(null, 
                    "错误: " + e.getMessage() + "\n\n详细信息请查看控制台", 
                    "MainFrame测试失败", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
