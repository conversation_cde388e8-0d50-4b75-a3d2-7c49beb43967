package com.crtscan.controller;

import com.crtscan.model.ScanResult;
import com.crtscan.service.CertificateScanner;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * 主界面控制器
 */
public class MainController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);
    
    @FXML private TextField domainField;
    @FXML private TextArea cookieArea;
    @FXML private Button scanButton;
    @FXML private Button exportButton;
    @FXML private Button clearButton;
    @FXML private ProgressBar progressBar;
    @FXML private Label statusLabel;
    @FXML private Label resultCountLabel;
    @FXML private TableView<ScanResult.DomainInfo> resultsTable;
    @FXML private TableColumn<ScanResult.DomainInfo, Integer> indexColumn;
    @FXML private TableColumn<ScanResult.DomainInfo, String> domainColumn;
    @FXML private TableColumn<ScanResult.DomainInfo, String> typeColumn;
    @FXML private TextArea logArea;
    @FXML private CheckBox headlessCheckBox;
    @FXML private CheckBox verboseCheckBox;
    
    private CertificateScanner scanner;
    private ScanResult currentResult;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("初始化主界面控制器");
        
        // 初始化扫描器
        scanner = new CertificateScanner();
        
        // 设置表格列
        setupTableColumns();
        
        // 设置初始状态
        setupInitialState();
        
        // 绑定事件处理器
        setupEventHandlers();
        
        logger.info("主界面控制器初始化完成");
    }
    
    /**
     * 设置表格列
     */
    private void setupTableColumns() {
        indexColumn.setCellValueFactory(new PropertyValueFactory<>("index"));
        domainColumn.setCellValueFactory(new PropertyValueFactory<>("domain"));
        typeColumn.setCellValueFactory(new PropertyValueFactory<>("type"));
        
        // 设置列宽
        indexColumn.setPrefWidth(60);
        domainColumn.setPrefWidth(300);
        typeColumn.setPrefWidth(100);
        
        // 设置表格可选择多行
        resultsTable.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    }
    
    /**
     * 设置初始状态
     */
    private void setupInitialState() {
        progressBar.setVisible(false);
        exportButton.setDisable(true);
        statusLabel.setText("就绪");
        resultCountLabel.setText("结果: 0");
        
        // 设置默认值
        headlessCheckBox.setSelected(true);
        verboseCheckBox.setSelected(true);
        
        // 设置提示文本
        domainField.setPromptText("请输入域名，例如: mi.com");
        cookieArea.setPromptText("请输入Censys平台的cookie，例如: auth_v1=...");
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 扫描按钮事件
        scanButton.setOnAction(event -> startScan());
        
        // 导出按钮事件
        exportButton.setOnAction(event -> exportResults());
        
        // 清除按钮事件
        clearButton.setOnAction(event -> clearResults());
        
        // 域名输入框回车事件
        domainField.setOnAction(event -> startScan());
        
        // 表格双击事件
        resultsTable.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                ScanResult.DomainInfo selected = resultsTable.getSelectionModel().getSelectedItem();
                if (selected != null) {
                    showDomainDetails(selected);
                }
            }
        });
    }
    
    /**
     * 开始扫描
     */
    @FXML
    private void startScan() {
        String domain = domainField.getText().trim();
        String cookie = cookieArea.getText().trim();
        
        // 验证输入
        if (domain.isEmpty()) {
            showAlert("错误", "请输入要扫描的域名");
            return;
        }
        
        if (cookie.isEmpty()) {
            showAlert("错误", "请输入Censys平台的cookie");
            return;
        }
        
        // 清除之前的结果
        clearResults();
        
        // 设置UI状态
        setUIState(true);
        
        // 创建扫描任务
        Task<ScanResult> scanTask = new Task<ScanResult>() {
            @Override
            protected ScanResult call() throws Exception {
                updateMessage("正在扫描...");
                
                // 设置扫描器参数
                scanner.setCookie(cookie);
                scanner.setHeadless(headlessCheckBox.isSelected());
                scanner.setVerbose(verboseCheckBox.isSelected());
                
                // 设置进度回调
                scanner.setProgressCallback((message, progress) -> {
                    Platform.runLater(() -> {
                        updateMessage(message);
                        updateProgress(progress, 100);
                    });
                });
                
                // 设置日志回调
                scanner.setLogCallback(message -> {
                    Platform.runLater(() -> appendLog(message));
                });
                
                // 执行扫描
                return scanner.scanDomain(domain);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    currentResult = getValue();
                    displayResults(currentResult);
                    setUIState(false);
                    updateMessage("扫描完成");
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("扫描失败", exception);
                    showAlert("扫描失败", "扫描过程中发生错误: " + exception.getMessage());
                    setUIState(false);
                    updateMessage("扫描失败");
                });
            }
        };
        
        // 绑定进度和状态
        progressBar.progressProperty().bind(scanTask.progressProperty());
        statusLabel.textProperty().bind(scanTask.messageProperty());
        
        // 在后台线程中执行扫描
        Thread scanThread = new Thread(scanTask);
        scanThread.setDaemon(true);
        scanThread.start();
    }
    
    /**
     * 设置UI状态
     */
    private void setUIState(boolean scanning) {
        scanButton.setDisable(scanning);
        domainField.setDisable(scanning);
        cookieArea.setDisable(scanning);
        headlessCheckBox.setDisable(scanning);
        verboseCheckBox.setDisable(scanning);
        progressBar.setVisible(scanning);
        
        if (!scanning) {
            progressBar.progressProperty().unbind();
            statusLabel.textProperty().unbind();
            progressBar.setProgress(0);
        }
    }
    
    /**
     * 显示扫描结果
     */
    private void displayResults(ScanResult result) {
        if (result == null) {
            return;
        }
        
        List<ScanResult.DomainInfo> domains = result.getDomains();
        resultsTable.getItems().setAll(domains);
        
        resultCountLabel.setText(String.format("结果: %d", domains.size()));
        exportButton.setDisable(domains.isEmpty());
        
        // 添加结果摘要到日志
        appendLog(String.format("扫描完成! 目标域名: %s", result.getTargetDomain()));
        appendLog(String.format("证书SHA-256: %s", result.getSha256()));
        appendLog(String.format("找到 %d 个使用相同证书的域名/IP", domains.size()));
    }
    
    /**
     * 导出结果
     */
    @FXML
    private void exportResults() {
        if (currentResult == null || currentResult.getDomains().isEmpty()) {
            showAlert("提示", "没有可导出的结果");
            return;
        }
        
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导出扫描结果");
        fileChooser.setInitialFileName("crt_scan_results_" + 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("文本文件", "*.txt")
        );
        
        Stage stage = (Stage) exportButton.getScene().getWindow();
        File file = fileChooser.showSaveDialog(stage);
        
        if (file != null) {
            try {
                exportToFile(file);
                showAlert("成功", "结果已导出到: " + file.getAbsolutePath());
            } catch (IOException e) {
                logger.error("导出文件失败", e);
                showAlert("错误", "导出文件失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 导出到文件
     */
    private void exportToFile(File file) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("CRT证书扫描结果报告\n");
            writer.write("=".repeat(50) + "\n");
            writer.write("目标域名: " + currentResult.getTargetDomain() + "\n");
            writer.write("证书SHA-256: " + currentResult.getSha256() + "\n");
            writer.write("扫描时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("找到 " + currentResult.getDomains().size() + " 个使用相同证书的域名/IP:\n\n");
            
            for (int i = 0; i < currentResult.getDomains().size(); i++) {
                ScanResult.DomainInfo domain = currentResult.getDomains().get(i);
                writer.write(String.format("%3d. %s (%s)\n", i + 1, domain.getDomain(), domain.getType()));
            }
        }
    }
    
    /**
     * 清除结果
     */
    @FXML
    private void clearResults() {
        resultsTable.getItems().clear();
        logArea.clear();
        resultCountLabel.setText("结果: 0");
        exportButton.setDisable(true);
        currentResult = null;
    }
    
    /**
     * 显示域名详情
     */
    private void showDomainDetails(ScanResult.DomainInfo domainInfo) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("域名详情");
        alert.setHeaderText(domainInfo.getDomain());
        alert.setContentText(String.format("类型: %s\n索引: %d", domainInfo.getType(), domainInfo.getIndex()));
        alert.showAndWait();
    }
    
    /**
     * 添加日志
     */
    private void appendLog(String message) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        logArea.appendText(String.format("[%s] %s\n", timestamp, message));
    }
    
    /**
     * 显示警告对话框
     */
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
