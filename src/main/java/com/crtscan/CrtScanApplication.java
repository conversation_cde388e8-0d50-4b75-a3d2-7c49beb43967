package com.crtscan;

import com.crtscan.gui.MainFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;

/**
 * 证书扫描工具 - Swing应用程序主类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CrtScanApplication {

    private static final Logger logger = LoggerFactory.getLogger(CrtScanApplication.class);

    /**
     * 应用程序入口点
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("启动CRT扫描应用程序...");
        logger.info("启动CRT扫描应用程序...");

        try {
            System.out.println("设置系统外观...");
            // 设置系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());

            System.out.println("创建GUI...");
            // 在事件调度线程中创建和显示GUI
            SwingUtilities.invokeLater(() -> {
                try {
                    System.out.println("创建MainFrame实例...");
                    MainFrame frame = new MainFrame();
                    System.out.println("显示MainFrame...");
                    frame.setVisible(true);
                    System.out.println("应用程序启动成功！");
                    logger.info("应用程序启动成功");
                } catch (Exception e) {
                    System.err.println("启动应用程序时发生错误:");
                    e.printStackTrace();
                    logger.error("启动应用程序时发生错误", e);
                    JOptionPane.showMessageDialog(null,
                        "启动应用程序失败: " + e.getMessage(),
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
                }
            });

        } catch (Exception e) {
            System.err.println("设置外观时发生错误:");
            e.printStackTrace();
            logger.error("设置外观时发生错误", e);
            // 如果设置外观失败，仍然尝试启动应用程序
            SwingUtilities.invokeLater(() -> {
                try {
                    System.out.println("备用方式创建MainFrame...");
                    MainFrame frame = new MainFrame();
                    frame.setVisible(true);
                } catch (Exception ex) {
                    System.err.println("启动应用程序失败:");
                    ex.printStackTrace();
                    logger.error("启动应用程序失败", ex);
                    JOptionPane.showMessageDialog(null,
                        "启动应用程序失败: " + ex.getMessage(),
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
                }
            });
        }
    }
}
