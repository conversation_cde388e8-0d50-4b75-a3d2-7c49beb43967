package com.crtscan.model;

import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 扫描结果模型类
 */
public class ScanResult {
    
    private String targetDomain;
    private String sha256;
    private List<String> certIds;
    private List<DomainInfo> domains;
    private boolean success;
    private String error;
    
    public ScanResult() {
        this.certIds = new ArrayList<>();
        this.domains = new ArrayList<>();
        this.success = false;
    }
    
    public ScanResult(String targetDomain) {
        this();
        this.targetDomain = targetDomain;
    }
    
    // Getters and Setters
    public String getTargetDomain() {
        return targetDomain;
    }
    
    public void setTargetDomain(String targetDomain) {
        this.targetDomain = targetDomain;
    }
    
    public String getSha256() {
        return sha256;
    }
    
    public void setSha256(String sha256) {
        this.sha256 = sha256;
    }
    
    public List<String> getCertIds() {
        return certIds;
    }
    
    public void setCertIds(List<String> certIds) {
        this.certIds = certIds;
    }
    
    public List<DomainInfo> getDomains() {
        return domains;
    }
    
    public void setDomains(List<DomainInfo> domains) {
        this.domains = domains;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
    
    /**
     * 添加域名信息
     */
    public void addDomain(String domain, String type) {
        domains.add(new DomainInfo(domains.size() + 1, domain, type));
    }
    
    /**
     * 域名信息内部类，用于TableView显示
     */
    public static class DomainInfo {
        private final IntegerProperty index;
        private final StringProperty domain;
        private final StringProperty type;
        
        public DomainInfo(int index, String domain, String type) {
            this.index = new SimpleIntegerProperty(index);
            this.domain = new SimpleStringProperty(domain);
            this.type = new SimpleStringProperty(type);
        }
        
        // Index property
        public IntegerProperty indexProperty() {
            return index;
        }
        
        public int getIndex() {
            return index.get();
        }
        
        public void setIndex(int index) {
            this.index.set(index);
        }
        
        // Domain property
        public StringProperty domainProperty() {
            return domain;
        }
        
        public String getDomain() {
            return domain.get();
        }
        
        public void setDomain(String domain) {
            this.domain.set(domain);
        }
        
        // Type property
        public StringProperty typeProperty() {
            return type;
        }
        
        public String getType() {
            return type.get();
        }
        
        public void setType(String type) {
            this.type.set(type);
        }
        
        @Override
        public String toString() {
            return String.format("%d. %s (%s)", getIndex(), getDomain(), getType());
        }
    }
    
    @Override
    public String toString() {
        return String.format("ScanResult{targetDomain='%s', sha256='%s', domains=%d, success=%s}", 
                targetDomain, sha256, domains.size(), success);
    }
}
