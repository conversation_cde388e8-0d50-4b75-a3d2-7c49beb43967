#!/bin/bash

# CRT证书扫描工具 - Java版本构建脚本

echo "=========================================="
echo "CRT证书扫描工具 - Java版本构建脚本"
echo "=========================================="

# 检查Java版本
echo "检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java，请确保已安装Java 8或更高版本"
    exit 1
fi

# 检查Maven
echo "检查Maven..."
mvn -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Maven，请确保已安装Maven"
    exit 1
fi

# 清理之前的构建
echo "清理之前的构建..."
mvn clean

# 编译项目
echo "编译项目..."
mvn compile
if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

# 打包项目
echo "打包项目..."
mvn package -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: 打包失败"
    exit 1
fi

# 检查生成的JAR文件
JAR_FILE="target/crt-scan-java-1.0.0-shaded.jar"
if [ -f "$JAR_FILE" ]; then
    echo "=========================================="
    echo "构建成功！"
    echo "生成的JAR文件: $JAR_FILE"
    echo "文件大小: $(du -h $JAR_FILE | cut -f1)"
    echo "=========================================="
    echo ""
    echo "运行方法:"
    echo "java -jar $JAR_FILE"
    echo ""
else
    echo "错误: 未找到生成的JAR文件"
    exit 1
fi
