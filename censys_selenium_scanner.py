#!/usr/bin/env python3
"""
使用Selenium的Censys SHA256扫描工具
能够处理JavaScript渲染的搜索结果
"""

import time
import argparse
import re
from urllib.parse import quote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

class CensysSeleniumScanner:
    def __init__(self, cookie=None, verbose=True, headless=True):
        self.cookie = cookie
        self.verbose = verbose
        self.headless = headless
        self.driver = None
        
    def log(self, message, level="INFO"):
        """输出日志信息"""
        if self.verbose:
            timestamp = time.strftime('%H:%M:%S')
            print(f"[{timestamp}] {level}: {message}")
    
    def setup_driver(self):
        """设置Chrome WebDriver"""
        self.log("设置Chrome WebDriver...")
        
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # 添加更多选项来模拟真实浏览器
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.log("Chrome WebDriver设置成功")
            return True
        except Exception as e:
            self.log(f"设置Chrome WebDriver失败: {e}", "ERROR")
            return False
    
    def set_cookies(self):
        """设置cookies"""
        if not self.cookie:
            self.log("未提供cookie", "WARN")
            return
        
        self.log("设置cookies...")
        
        # 首先访问主页以设置域名
        self.driver.get("https://platform.censys.io/")
        
        # 解析并设置cookies
        for cookie_pair in self.cookie.split(';'):
            if '=' in cookie_pair:
                key, value = cookie_pair.strip().split('=', 1)
                self.driver.add_cookie({
                    'name': key,
                    'value': value,
                    'domain': '.censys.io'
                })
        
        self.log("Cookies设置完成")
    
    def search_censys(self, sha256):
        """搜索Censys并获取结果"""
        if not self.setup_driver():
            return {"error": "无法设置WebDriver"}
        
        try:
            # 设置cookies
            self.set_cookies()
            
            # 构建搜索URL
            search_query = f'cert.fingerprint_sha256 = "{sha256}"'
            url = f"https://platform.censys.io/search?q={quote(search_query)}"
            
            self.log(f"访问搜索URL: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            self.log("等待页面加载...")
            time.sleep(5)
            
            # 检查是否遇到Cloudflare验证
            if "正在验证您是否是真人" in self.driver.page_source or "Just a moment" in self.driver.page_source:
                self.log("检测到Cloudflare验证，等待通过...")

                # 等待Cloudflare验证完成，最多等待60秒
                for i in range(60):
                    time.sleep(1)
                    if "正在验证您是否是真人" not in self.driver.page_source and "Just a moment" not in self.driver.page_source:
                        self.log("Cloudflare验证已通过")
                        break
                    if i % 10 == 0:
                        self.log(f"仍在等待Cloudflare验证... ({i+1}/60秒)")
                else:
                    self.log("Cloudflare验证超时", "ERROR")
                    return {"error": "Cloudflare验证超时", "all_names": [], "html": ""}

            # 等待搜索结果加载完成
            self.log("等待搜索结果...")
            try:
                # 等待加载指示器消失
                WebDriverWait(self.driver, 30).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="search-loading"]'))
                )
                self.log("搜索加载完成")
            except TimeoutException:
                self.log("等待搜索结果超时，尝试继续...", "WARN")

            # 额外等待确保内容完全加载
            time.sleep(5)
            
            # 获取页面源码
            page_source = self.driver.page_source
            
            # 保存页面源码用于调试
            with open(f'selenium_response_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            self.log("页面源码已保存")
            
            # 解析搜索结果
            all_names = self.extract_all_names(page_source)
            
            return {
                "all_names": all_names,
                "html": page_source,
                "error": None
            }
            
        except Exception as e:
            self.log(f"搜索过程中出错: {e}", "ERROR")
            return {"error": str(e), "all_names": [], "html": ""}
        
        finally:
            if self.driver:
                self.driver.quit()
    
    def extract_all_names(self, html_content):
        """从HTML中提取All Names内容"""
        all_names = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找证书相关的卡片或容器
            certificate_cards = soup.find_all(['div', 'section'], class_=re.compile(r'card|result|certificate', re.I))
            
            for card in certificate_cards:
                # 在每个卡片中查找域名和IP
                text_content = card.get_text()
                
                # 使用正则表达式查找域名
                domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
                domains = re.findall(domain_pattern, text_content)
                
                # 使用正则表达式查找IP地址
                ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
                ips = re.findall(ip_pattern, text_content)
                
                for item in domains + ips:
                    if self.is_valid_domain_or_ip(item):
                        all_names.append(item)
            
            # 如果没有找到结果，尝试在整个页面中搜索
            if not all_names:
                self.log("在卡片中未找到结果，搜索整个页面...")
                
                # 查找所有可能包含域名的元素
                elements = soup.find_all(['span', 'div', 'td', 'p'], string=re.compile(r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'))
                
                for element in elements:
                    text = element.get_text().strip()
                    if self.is_valid_domain_or_ip(text):
                        all_names.append(text)
                
                # 使用正则表达式在整个HTML中搜索
                domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
                ip_pattern = r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
                
                all_domains = re.findall(domain_pattern, html_content)
                all_ips = re.findall(ip_pattern, html_content)
                
                for item in all_domains + all_ips:
                    if self.is_valid_domain_or_ip(item):
                        all_names.append(item)
            
            # 去重并过滤
            all_names = list(set(all_names))
            
            # 过滤掉明显不相关的域名
            filtered_names = []
            exclude_patterns = [
                'censys.io', 'google.com', 'cloudflare.com', 'amazonaws.com',
                'gstatic.com', 'googleapis.com', 'doubleclick.net', 'googlesyndication.com',
                'example.com', 'localhost', '127.0.0.1', '0.0.0.0'
            ]
            
            for name in all_names:
                if not any(exclude in name.lower() for exclude in exclude_patterns):
                    # 额外过滤：排除明显的文件名
                    if not name.endswith(('.js', '.css', '.png', '.jpg', '.gif', '.svg')):
                        filtered_names.append(name)
            
            self.log(f"提取到 {len(filtered_names)} 个有效的域名/IP")
            return sorted(filtered_names)
            
        except Exception as e:
            self.log(f"提取All Names失败: {e}", "ERROR")
            return []
    
    def is_valid_domain_or_ip(self, text):
        """验证是否为有效的域名或IP地址"""
        if not text or len(text) > 100 or len(text) < 3:
            return False
        
        # 检查是否包含点号
        if '.' not in text:
            return False
        
        # 排除明显不是域名的文本
        invalid_chars = ['<', '>', '"', "'", '(', ')', '[', ']', '{', '}', ' ', '\n', '\t', '\\', '/']
        if any(char in text for char in invalid_chars):
            return False
        
        # 简单的IP地址检查
        parts = text.split('.')
        if len(parts) == 4:
            try:
                for part in parts:
                    if not part.isdigit():
                        break
                    num = int(part)
                    if not (0 <= num <= 255):
                        break
                else:
                    return True
            except ValueError:
                pass
        
        # 简单的域名检查
        if len(parts) >= 2:
            for part in parts:
                if not part or not all(c.isalnum() or c == '-' for c in part):
                    return False
                if part.startswith('-') or part.endswith('-'):
                    return False
            return True
        
        return False


def main():
    parser = argparse.ArgumentParser(description='使用Selenium的Censys SHA256扫描工具')
    parser.add_argument('sha256', help='证书SHA256指纹')
    parser.add_argument('--cookie', help='Censys平台的cookie字符串')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    parser.add_argument('--verbose', '-v', action='store_true', default=True, help='显示详细信息')
    parser.add_argument('--no-headless', action='store_true', help='显示浏览器窗口（调试用）')
    
    args = parser.parse_args()
    
    scanner = CensysSeleniumScanner(
        cookie=args.cookie,
        verbose=args.verbose,
        headless=not args.no_headless
    )
    
    print("=" * 60)
    print("Censys Selenium SHA256扫描工具")
    print("=" * 60)
    print(f"目标SHA256: {args.sha256}")
    print("=" * 60)
    
    # 执行搜索
    result = scanner.search_censys(args.sha256)
    
    if result["error"]:
        print(f"\n错误: {result['error']}")
        return
    
    all_names = result["all_names"]
    
    print(f"\n找到 {len(all_names)} 个域名/IP:")
    print("=" * 60)
    
    if all_names:
        for i, name in enumerate(all_names, 1):
            print(f"{i:3d}. {name}")
        
        # 保存到文件
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"Censys SHA256搜索结果\n")
                    f.write(f"SHA256: {args.sha256}\n")
                    f.write(f"搜索时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"找到 {len(all_names)} 个域名/IP:\n\n")
                    for name in all_names:
                        f.write(f"{name}\n")
                print(f"\n[+] 结果已保存到: {args.output}")
            except Exception as e:
                print(f"[-] 保存文件失败: {e}")
    else:
        print("未找到相关的域名/IP")
    
    print("\n搜索完成!")


if __name__ == "__main__":
    main()
