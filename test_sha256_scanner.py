#!/usr/bin/env python3
"""
测试Censys SHA256扫描工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from censys_sha256_scanner import CensysSHA256Scanner

def test_sha256_validation():
    """测试SHA256验证功能"""
    print("测试SHA256验证功能...")
    
    scanner = CensysSHA256Scanner(verbose=False)
    
    # 测试有效的SHA256
    valid_sha256 = "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469"
    assert scanner.validate_sha256(valid_sha256), "有效SHA256验证失败"
    
    # 测试无效的SHA256
    invalid_cases = [
        "",  # 空字符串
        "invalid",  # 太短
        "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469x",  # 包含无效字符
        "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd268446",  # 太短
        "e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469a",  # 太长
    ]
    
    for invalid_sha256 in invalid_cases:
        assert not scanner.validate_sha256(invalid_sha256), f"无效SHA256 '{invalid_sha256}' 应该被拒绝"
    
    print("✓ SHA256验证功能测试通过")

def test_domain_ip_validation():
    """测试域名/IP验证功能"""
    print("测试域名/IP验证功能...")
    
    scanner = CensysSHA256Scanner(verbose=False)
    
    # 测试有效的域名
    valid_domains = [
        "example.com",
        "www.example.com",
        "api.sub.example.com",
        "test-site.org",
        "***********",
        "********",
        "*******",
    ]
    
    for domain in valid_domains:
        assert scanner.is_valid_domain_or_ip(domain), f"有效域名/IP '{domain}' 验证失败"
    
    # 测试无效的域名/IP
    invalid_cases = [
        "",  # 空字符串
        "invalid",  # 没有点号
        "example.com<script>",  # 包含无效字符
        "256.256.256.256",  # 无效IP
        "example..com",  # 双点号
        "-example.com",  # 以连字符开头
        "example.com-",  # 以连字符结尾
    ]
    
    for invalid_case in invalid_cases:
        assert not scanner.is_valid_domain_or_ip(invalid_case), f"无效域名/IP '{invalid_case}' 应该被拒绝"
    
    print("✓ 域名/IP验证功能测试通过")

def test_basic_functionality():
    """测试基本功能（不需要网络连接）"""
    print("测试基本功能...")
    
    scanner = CensysSHA256Scanner(verbose=False)
    
    # 测试HTML解析功能
    sample_html = """
    <html>
    <body>
        <table>
            <tr>
                <td data-testid="ip-address">***********</td>
                <td data-testid="host-name">example.com</td>
            </tr>
            <tr>
                <td>********</td>
                <td>test.example.org</td>
            </tr>
        </table>
        <a href="/hosts/api.example.com">api.example.com</a>
        <div>Some text with domain.example.net in it</div>
    </body>
    </html>
    """
    
    names = scanner.extract_all_names(sample_html)
    
    # 验证提取的域名/IP
    expected_names = ["***********", "example.com", "********", "test.example.org", "api.example.com", "domain.example.net"]
    
    for expected in expected_names:
        assert expected in names, f"应该提取到 '{expected}'"
    
    print(f"✓ 从示例HTML中提取到 {len(names)} 个域名/IP")
    print("✓ 基本功能测试通过")

def main():
    """运行所有测试"""
    print("=" * 50)
    print("Censys SHA256扫描工具测试")
    print("=" * 50)
    
    try:
        test_sha256_validation()
        test_domain_ip_validation()
        test_basic_functionality()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！")
        print("=" * 50)
        
        # 提示用户如何进行实际测试
        print("\n要进行实际的网络测试，请运行：")
        print("python censys_sha256_scanner.py e4ad4fd18ebe01f3d92e2fdb0702a764b1642b43434237d3be5d85afd2684469")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
