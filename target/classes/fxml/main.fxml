<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.crtscan.controller.MainController">
   <top>
      <VBox spacing="10.0" styleClass="header-section">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         <children>
            <Label styleClass="title" text="CRT证书扫描工具">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Label styleClass="subtitle" text="查找使用相同证书的所有网站" />
            <Separator />
         </children>
      </VBox>
   </top>
   <center>
      <VBox spacing="15.0">
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         <children>
            <!-- 输入区域 -->
            <VBox spacing="10.0" styleClass="input-section">
               <children>
                  <Label styleClass="section-title" text="输入参数">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <!-- 域名输入 -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label minWidth="80.0" text="目标域名:" />
                        <TextField fx:id="domainField" prefWidth="300.0" promptText="例如: mi.com" />
                        <Region HBox.hgrow="ALWAYS" />
                        <CheckBox fx:id="headlessCheckBox" selected="true" text="无头模式" />
                        <CheckBox fx:id="verboseCheckBox" selected="true" text="详细日志" />
                     </children>
                  </HBox>
                  
                  <!-- Cookie输入 -->
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Censys Cookie:" />
                        <TextArea fx:id="cookieArea" prefRowCount="3" promptText="请输入Censys平台的cookie，例如: auth_v1=..." wrapText="true" />
                     </children>
                  </VBox>
                  
                  <!-- 操作按钮 -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Button fx:id="scanButton" prefWidth="100.0" styleClass="primary-button" text="开始扫描" />
                        <Button fx:id="exportButton" disable="true" prefWidth="100.0" text="导出结果" />
                        <Button fx:id="clearButton" prefWidth="100.0" text="清除结果" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Label fx:id="resultCountLabel" text="结果: 0" />
                     </children>
                  </HBox>
                  
                  <!-- 进度条和状态 -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <ProgressBar fx:id="progressBar" prefWidth="200.0" visible="false" />
                        <Label fx:id="statusLabel" text="就绪" />
                     </children>
                  </HBox>
               </children>
            </VBox>
            
            <!-- 结果区域 -->
            <VBox spacing="10.0" styleClass="results-section" VBox.vgrow="ALWAYS">
               <children>
                  <Label styleClass="section-title" text="扫描结果">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <!-- 结果表格 -->
                  <TableView fx:id="resultsTable" prefHeight="300.0" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="indexColumn" prefWidth="60.0" text="序号" />
                        <TableColumn fx:id="domainColumn" prefWidth="400.0" text="域名/IP" />
                        <TableColumn fx:id="typeColumn" prefWidth="100.0" text="类型" />
                     </columns>
                     <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                     </columnResizePolicy>
                  </TableView>
               </children>
            </VBox>
         </children>
      </VBox>
   </center>
   <bottom>
      <VBox spacing="5.0" styleClass="log-section">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         <children>
            <Label styleClass="section-title" text="日志信息">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
            <TextArea fx:id="logArea" editable="false" prefRowCount="6" wrapText="true" />
         </children>
      </VBox>
   </bottom>
</BorderPane>
