# CRT证书扫描工具 - Java版本

这是CRT证书扫描工具的Java图形化版本，使用JavaFX构建了用户友好的GUI界面。

## 功能特点

- 🖥️ **图形化界面**: 使用JavaFX构建的现代化GUI界面
- 🔍 **证书扫描**: 根据域名查询证书信息并找到使用相同证书的所有网站
- 📊 **结果展示**: 表格形式展示扫描结果，支持排序和筛选
- 📁 **结果导出**: 支持将扫描结果导出为文本文件
- 📝 **实时日志**: 实时显示扫描过程和详细日志信息
- ⚙️ **配置选项**: 支持无头模式、详细日志等配置选项

## 系统要求

- Java 11 或更高版本
- Maven 3.6 或更高版本
- Chrome浏览器（用于Selenium WebDriver）
- 网络连接

## 安装和构建

### 1. 克隆项目

```bash
git clone <repository-url>
cd crt-scan-java
```

### 2. 构建项目

使用提供的构建脚本：

```bash
./build.sh
```

或者手动使用Maven：

```bash
# 清理和编译
mvn clean compile

# 运行测试
mvn test

# 打包
mvn package
```

### 3. 运行应用程序

构建成功后，可以通过以下方式运行：

```bash
# 方式1: 直接运行JAR文件
java -jar target/crt-scan-java-1.0.0-shaded.jar

# 方式2: 使用Maven运行
mvn javafx:run
```

## 使用方法

### 1. 启动应用程序

运行JAR文件后，会打开图形化界面。

### 2. 输入参数

- **目标域名**: 输入要扫描的域名，例如 `mi.com`
- **Censys Cookie**: 输入从Censys平台获取的认证cookie

### 3. 配置选项

- **无头模式**: 勾选后浏览器将在后台运行（推荐）
- **详细日志**: 勾选后显示详细的扫描日志

### 4. 开始扫描

点击"开始扫描"按钮，程序将：

1. 查询目标域名的证书信息（通过crt.sh）
2. 获取证书的SHA-256指纹
3. 在Censys平台搜索使用相同证书的所有网站
4. 在表格中显示结果

### 5. 查看结果

- 扫描结果会显示在表格中，包括序号、域名/IP和类型
- 双击表格行可以查看详细信息
- 底部日志区域显示扫描过程的详细信息

### 6. 导出结果

点击"导出结果"按钮可以将扫描结果保存为文本文件。

## 获取Censys Cookie

1. 在浏览器中访问 https://platform.censys.io/
2. 完成登录
3. 按F12打开开发者工具
4. 进入Network标签页
5. 在Censys平台进行一次搜索
6. 找到搜索请求，复制Cookie头的值
7. 将完整的cookie字符串粘贴到应用程序中

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/crtscan/
│   │       ├── CrtScanApplication.java      # 主应用程序类
│   │       ├── controller/
│   │       │   └── MainController.java      # 主界面控制器
│   │       ├── model/
│   │       │   └── ScanResult.java          # 扫描结果模型
│   │       └── service/
│   │           └── CertificateScanner.java  # 证书扫描服务
│   └── resources/
│       ├── fxml/
│       │   └── main.fxml                    # 主界面FXML
│       ├── css/
│       │   └── style.css                    # 样式文件
│       ├── images/                          # 图标资源
│       └── logback.xml                      # 日志配置
├── pom.xml                                  # Maven配置
├── build.sh                                # 构建脚本
└── README.md                               # 说明文档
```

## 技术栈

- **JavaFX**: GUI框架
- **Selenium WebDriver**: 浏览器自动化
- **JSoup**: HTML解析
- **Apache HttpClient**: HTTP请求
- **SLF4J + Logback**: 日志框架
- **Maven**: 构建工具

## 故障排除

### 1. 无法启动应用程序

- 确保已安装Java 11或更高版本
- 确保JavaFX运行时可用

### 2. Chrome WebDriver错误

- 确保已安装Chrome浏览器
- WebDriverManager会自动下载对应版本的ChromeDriver

### 3. Censys访问失败

- 检查cookie是否有效
- 确保网络连接正常
- 可能需要等待Cloudflare验证通过

### 4. 内存不足

如果处理大量数据时出现内存不足，可以增加JVM内存：

```bash
java -Xmx2g -jar target/crt-scan-java-1.0.0-shaded.jar
```

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 请遵守Censys平台的使用条款和频率限制。本工具仅用于合法的安全研究和网络管理目的。
